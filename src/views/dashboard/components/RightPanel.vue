<template>
  <div class="right-panel">
    <div class="chart-container">
      <div class="chart-title">技能训练统计</div>
      <div class="chart-content">
        <div class="training-stats-layout" v-if="trainingStatsData.length">
          <!-- 左侧饼图 -->
          <div class="chart-visual">
            <e-charts ref="donutChart1" :option="donutChartOptions1" autoresize />
          </div>
          <!-- 右侧统计列表 -->
          <div class="stats-list">
            <div v-for="(item, index) in trainingStatsData" :key="`training-${item.abscissa || index}`" class="stats-item">
              <div class="stats-item-content">
                <div class="label-section">
                  <div
                    class="color-indicator"
                    :style="{ background: getColorByIndex(index) }"
                  ></div>
                  <span class="label-text">{{ item.abscissa }}</span>
                </div>
                <div class="value-text">{{ item.ordinate }}</div>
              </div>
            </div>
          </div>
        </div>
        <geega-empty v-if="!trainingStatsData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">技能考核统计</div>
      <div class="chart-content">
        <div class="training-stats-layout" v-if="assessmentStatsData.length">
          <div class="chart-visual assessment-chart-bg">
            <e-charts ref="donutChart2" :option="donutChartOptions2" autoresize />
          </div>
          <div class="stats-list">
            <div v-for="(item, index) in assessmentStatsData" :key="`assessment-${item.abscissa || index}`" class="stats-item">
              <div class="stats-item-content">
                <div class="label-section">
                  <div
                    class="color-indicator"
                    :style="{ background: getAssessmentColorByIndex(index) }"
                  ></div>
                  <span class="label-text">{{ item.abscissa }}</span>
                </div>
                <div class="value-text">{{ item.ordinate }}</div>
              </div>
            </div>
          </div>
        </div>
        <geega-empty v-if="!assessmentStatsData.length" description="暂无数据" />
      </div>
    </div>
    <div class="chart-container">
      <div class="chart-title">工位使用统计</div>
      <div class="chart-content">
        <e-charts
          ref="barChart"
          :option="barChartOptions"
          autoresize
          v-if="workstationStatsData.some((subArray) => subArray?.length)"
        />
        <geega-empty
          v-if="workstationStatsData.every((subArray) => !subArray?.length)"
          description="暂无数据"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, ref, onMounted, onUnmounted, watch, type PropType } from 'vue';
import {
  V1ManageLargeScreenChartActionDefectPost,
  V1ManageLargeScreenChartUnitUsePost,
} from '/@/api/cddc.req';
import type { APIModels } from '/@/api/cddc.model';
import type { EChartsOption } from 'echarts';
import ECharts from 'vue-echarts';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import {
  trainingGradientColors,
  assessmentGradientColors,
  workstationGradientColors,
} from './index';

const getColorByIndex = (index: number) => {
  const gradientColor = trainingGradientColors[index % trainingGradientColors.length];
  return `linear-gradient(135deg, ${gradientColor.start} 0%, ${gradientColor.end} 100%)`;
};

const getAssessmentColorByIndex = (index: number) => {
  const gradientColor = assessmentGradientColors[index % assessmentGradientColors.length];
  return `linear-gradient(135deg, ${gradientColor.start} 0%, ${gradientColor.end} 100%)`;
};

/**
 * 定义组件接收的属性
 */
const props = defineProps({
  /**
   * 选中的工艺
   */
  selectedCraft: {
    type: String,
    default: 'tighten',
  },
  refreshInterval: {
    type: Number,
    default: 5 * 60 * 1000, // 默认5分钟
  },
});

/**
 * 技能训练统计数据
 */
const trainingStatsData = ref<
  APIModels['V1ManageLargeScreenChartActionDefectPostResponseResult'][]
>([]);

/**
 * 技能考核统计数据
 */
const assessmentStatsData = ref<
  APIModels['V1ManageLargeScreenChartActionDefectPostResponseResult'][]
>([]);

/**
 * 工位使用统计数据
 */
const workstationStatsData = ref<
  APIModels['V1ManageLargeScreenChartUnitUsePostResponseResult'][][]
>([]);

/**
 * 定时器实例
 */
let refreshTimer: NodeJS.Timeout | null = null;

/**
 * 刷新图表数据
 * 注意：排名数据现在由单独的定时器处理
 */
const refreshChartData = () => {
  getTrainingStatsData();
  getAssessmentStatsData();
  getWorkstationStatsData();
};

/**
 * 启动图表刷新定时器
 */
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(refreshChartData, props.refreshInterval);
};

/**
 * 清除图表刷新定时器
 */
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

/**
 * 获取技能训练统计数据
 */
const getTrainingStatsData = async () => {
  try {
    const res = await V1ManageLargeScreenChartActionDefectPost({
      type: props.selectedCraft,
      projectType: 1,
    });
    trainingStatsData.value = res || [];
  } catch (error) {
    console.error('获取技能训练统计数据失败', error);
  }
};

/**
 * 获取技能考核统计数据
 */
const getAssessmentStatsData = async () => {
  try {
    const res = await V1ManageLargeScreenChartActionDefectPost({
      type: props.selectedCraft,
      projectType: 2,
    });
    assessmentStatsData.value = res || [];
  } catch (error) {
    console.error('获取技能考核统计数据失败', error);
  }
};

/**
 * 获取工位使用统计数据
 */
const getWorkstationStatsData = async () => {
  try {
    const projectTypes = [1, 2, 3]; // 1-训练项目, 2-考核项目, 3-比赛项目
    const promises = projectTypes.map((projectType) =>
      V1ManageLargeScreenChartUnitUsePost({
        type: props.selectedCraft,
        projectType,
      })
    );
    const results = await Promise.all(promises);
    workstationStatsData.value = results.map((res) => res || []);
  } catch (error) {
    console.error('获取工位使用统计数据失败', error);
    workstationStatsData.value = [];
  }
};

onMounted(() => {
  refreshChartData(); // 初始加载数据
  startRefreshTimer(); // 启动图表刷新定时器
});

onUnmounted(() => {
  stopRefreshTimer(); // 组件卸载时清除图表刷新定时器
});

watch(
  () => props.selectedCraft,
  () => {
    refreshChartData();
  }
);

watch(
  () => props.refreshInterval,
  (newInterval) => {
    stopRefreshTimer();
    startRefreshTimer();
  }
);

/**
 * 环形图1配置 - 技能训练统计
 */
const donutChartOptions1 = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    position: function (point, params, dom, rect, size) {
      // 始终在右侧显示
      return [point[0] + 20, point[1] - size.contentSize[1] / 2];
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  legend: {
    show: false,
  },
  series: [
    {
      name: '技能训练统计',
      type: 'pie',
      radius: [20, 75],
      avoidLabelOverlap: false,
      itemStyle: {
        borderWidth: 2,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
      },
      roseType: 'radius',
      label: {
        show: false,
      },
      emphasis: {
        label: {
          show: false,
        },
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(64, 158, 255, 0.6)',
        },
      },
      labelLine: {
        show: false,
      },
      data: [
        ...(trainingStatsData.value?.map((item, index) => {
          const gradientColor = trainingGradientColors[index % trainingGradientColors.length];
          return {
            value: item.ordinate,
            name: item.abscissa,
            itemStyle: {
              color: {
                type: 'linear' as const,
                x: 1,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  { offset: 0, color: gradientColor.start },
                  { offset: 1, color: gradientColor.end },
                ],
              },
            },
          };
        }) || []),
      ],
    },
  ],
}));

/**
 * 环形图2配置 - 技能考核统计
 */
const donutChartOptions2 = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(1, 22, 39, 0.95)',
    borderColor: 'rgba(64, 158, 255, 0.5)',
    borderWidth: 1,
    textStyle: {
      fontSize: 14,
      color: '#fff',
    },
    position: function (point, params, dom, rect, size) {
      // 始终在右侧显示
      return [point[0] + 20, point[1] - size.contentSize[1] / 2];
    },
    className: 'dark-ignore-style echarts-tooltip-dark',
  },
  legend: {
    show: false,
  },

  series: [
    {
      name: '技能考核统计',
      type: 'pie',
      radius: ['60%', '90%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderWidth: 2,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
      },
      label: {
        show: false,
      },
      emphasis: {
        label: {
          show: false,
        },
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(64, 158, 255, 0.6)',
        },
      },
      labelLine: {
        show: false,
      },
      data: [
        ...(assessmentStatsData.value?.map((item, index) => {
          const gradientColor = assessmentGradientColors[index % assessmentGradientColors.length];
          return {
            value: item.ordinate,
            name: item.abscissa,
            itemStyle: {
              color: {
                type: 'linear' as const,
                x: 1,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  { offset: 0, color: gradientColor.start },
                  { offset: 1, color: gradientColor.end },
                ],
              },
            },
          };
        }) || []),
      ],
    },
  ],
}));

/**
 * 条形图配置 - 工位使用统计
 */
const barChartOptions = computed<EChartsOption>(() => {
  // y-axis categories, matching project types 1, 2, 3
  const yAxisCategories = ['训练', '考核', '比赛'];

  // Get all unique workstation names from all project types
  const allWorkstations = new Set<string>();
  workstationStatsData.value.forEach((dataSet) => {
    if (dataSet) {
      dataSet.forEach((item) => {
        allWorkstations.add(item.abscissa);
      });
    }
  });
  const legendData = Array.from(allWorkstations);

  // Map data for easy lookup: { '工位1': [10, 20, 5], '工位2': [15, 8, 30], ... }
  // The array corresponds to yAxisCategories
  const seriesDataMap = new Map<string, number[]>();
  legendData.forEach((ws) => {
    seriesDataMap.set(ws, new Array(yAxisCategories.length).fill(0));
  });

  workstationStatsData.value.forEach((dataSet, projectTypeIndex) => {
    if (dataSet) {
      dataSet.forEach((item) => {
        if (seriesDataMap.has(item.abscissa)) {
          // 将秒转换为分钟，保留1位小数
          const valueInMinutes = Math.round((item.ordinate / 60) * 10) / 10;
          seriesDataMap.get(item.abscissa)![projectTypeIndex] = valueInMinutes;
        }
      });
    }
  });

  const series = legendData.map((workstationName, index) => {
    const gradientColor = workstationGradientColors[index % workstationGradientColors.length];
    return {
      name: workstationName,
      type: 'bar' as const,
      data: seriesDataMap.get(workstationName) || [],
      itemStyle: {
        color: {
          type: 'linear' as const,
          x: 1,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            { offset: 0, color: gradientColor.start },
            { offset: 1, color: gradientColor.end },
          ],
        },
      },
    };
  });

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(1, 22, 39, 0.95)',
      borderColor: 'rgba(64, 158, 255, 0.5)',
      borderWidth: 1,
      textStyle: {
        fontSize: 14,
        color: '#fff',
      },
      formatter: function (params: any) {
        let html = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach((param: any, index: number) => {
          const gradientColor = workstationGradientColors[index % workstationGradientColors.length];
          const gradientStyle = `linear-gradient(270deg, ${gradientColor.start} 0%, ${gradientColor.end} 100%)`;

          // 将分钟值转换回秒数，然后格式化为分秒形式
          const totalSeconds = Math.round(param.value * 60);
          let timeDisplay = '';

          if (totalSeconds % 60 === 0) {
            // 如果恰好是整数分钟，只显示分钟
            timeDisplay = `${totalSeconds / 60}分`;
          } else {
            // 否则显示分秒形式
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            if (minutes > 0) {
              timeDisplay = `${minutes}分${seconds}秒`;
            } else {
              timeDisplay = `${seconds}秒`;
            }
          }

          html += `<div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${gradientStyle}; margin-right: 8px; border-radius: 2px;"></span>
            <span style="margin-right: 8px;">${param.seriesName}:</span>
            <span style="font-weight: bold;">${timeDisplay}</span>
          </div>`;
        });
        return html;
      },
      className: 'dark-ignore-style echarts-tooltip-dark',
    },
    legend: {
      show: true,
      data: legendData,
      textStyle: {
        color: 'rgba(255, 255, 255, 0.9)',
      },
      top: 5,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%', // Make space for legend
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)',
        },
      },
    },
    yAxis: {
      type: 'category',
      data: yAxisCategories,
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
      },
    },
    series,
  };
});
</script>

<style lang="less" scoped>
.right-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .chart-container {
    border-radius: 3px;
    flex: 1;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(64, 158, 255, 0.4);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
    }

    .chart-title {
      font-size: 21px;
      font-weight: bold;
      margin-bottom: 9px;
      color: #fff;
      padding-left: 30px;
    }

    .chart-content {
      display: flex;
      justify-content: center;
      height: calc(100% - 45px);
    }
  }
}

.training-stats-layout {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
  align-items: center;
}

.chart-visual {
  width: 164px;
  height: 100%;
  margin-left: 7px;
}

.assessment-chart-bg {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 164px;
    height: 164px;
    background: url('/@/assets/images/screen/dp-bg-chart.png') no-repeat center / 100% 100%;
    z-index: 1;
    pointer-events: none;
  }

  > * {
    position: relative;
    z-index: 2;
  }
}

.stats-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-right: 8px;
}

.stats-item {
  --ignore-bg-color: #232f41;
  background-color: var(--ignore-bg-color);
  border-radius: 3px;
  padding: 8px 12px;
}

.stats-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-indicator {
  width: 8px;
  height: 8px;
  border: 0.5px solid #ffffff;
}

.label-text {
  font-size: 14px;
  --ignore-color: #aabac9;
  color: var(--ignore-color);
}

.value-text {
  font-size: 16px;
  --ignore-color: #f8fffd;
  color: var(--ignore-color);
  font-weight: 600;
}
</style>
