import {
  V1ManageCommonProjectTypeEnum,
  V1ManageTrainStudyRecordsProjects,
  V1ManageTrainStudyRecordsUsers,
} from '/@/api/cddc.req';
import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import { unitTree } from '/@/api/admin/project';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

dayjs.extend(duration);

// 表格配置
export const columns: EnhancedColumn[] = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 120,
    search: {
      field: 'projectIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        mode: 'multiple',
        maxTagCount: 'responsive',
        optionFilterProp: 'label',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsProjects({});
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '项目类型',
    dataIndex: 'countAlgorithmShow',
    width: 100,
    search: {
      field: 'countAlgorithm',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showArrow: true,
        api: V1ManageCommonProjectTypeEnum,
        fieldNames: {
          label: 'desc',
          value: 'code',
          key: 'code',
        },
      },
    },
  },
  {
    title: '人员姓名',
    dataIndex: 'userName',
    width: 120,
    search: {
      field: 'userIdList',
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        maxTagCount: 'responsive',
        api: async () => {
          const resp = await V1ManageTrainStudyRecordsUsers();
          return resp.map((item) => ({ ...item, label: item.name, value: item.id }));
        },
      },
    },
  },
  {
    title: '操作工位',
    dataIndex: 'locationName',
    width: 120,
    search: {
      field: 'locationIdList',
      component: 'ApiTreeSelect',
      componentProps: {
        api: async () => {
          const res = await unitTree();
          // 筛选只有type为STATION能选择
          const filterStations = (nodes: any[]): any[] => {
            return nodes.filter((node) => {
              if (node.type !== 'STATION') {
                node.disabled = true;
              }
              if (node.childNodes && node.childNodes.length) {
                node.childNodes = filterStations(node.childNodes);
                return node.childNodes.length > 0;
              }
              return true;
            });
          };
          return filterStations(res);
        },
        fieldNames: {
          label: 'name',
          value: 'id',
          key: 'id',
          children: 'childNodes',
        },
        placeholder: '请选择',
        allowClear: true,
        showSearch: true,
        showArrow: true,
        multiple: true,
        maxTagCount: 'responsive',
        treeNodeFilterProp: 'name',
        // 默认展开2层，根据用户偏好设置
        treeDefaultExpandAll: true,
      },
    },
  },
  {
    title: '作业次数',
    dataIndex: 'opNum',
    width: 100,
  },
  {
    title: '操作时长',
    dataIndex: 'trainDuration',
    width: 100,
    customRender: ({ record }) => {
      const durationInSeconds = record.trainDuration || 0;
      const duration = dayjs.duration(durationInSeconds, 'second');

      const hours = Math.floor(duration.asHours());
      const minutes = duration.minutes();
      const seconds = duration.seconds();

      if (hours > 0) {
        return seconds > 0
          ? `${hours}h${minutes}min${seconds}s`
          : (minutes > 0 ? `${hours}h${minutes}min` : `${hours}h`);
      } else if (minutes > 0) {
        return seconds > 0 ? `${minutes}min${seconds}s` : `${minutes}min`;
      } else {
        return `${seconds}s`;
      }
    },
  },
  {
    title: '合格率',
    dataIndex: 'passRate',
    width: 100,
    customRender: ({ text }) => {
      return `${text}%`;
    },
  },
  {
    title: '达标率',
    dataIndex: 'actionPassRate',
    width: 100,
    customRender: ({ text }) => {
      return `${text}%`;
    },
  },
  {
    title: '操作时间',
    dataIndex: 'operationTime',
    width: 180,
    search: {
      field: 'timeRange',
      label: '操作时间',
      component: 'RangePicker',
      componentProps: {
        allowClear: true,
        showTime: { format: 'HH:mm:ss' },
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
        style: { width: '100%' },
      },
      colProps: { span: 6 },
    },
  },
];
