<template>
  <div class="operation-log">
    <ScreenTitle>操作日志</ScreenTitle>
    <ScreenBox class="flex-1">
      <div class="log-list box-content">
        <template v-if="logList?.length">
          <div class="log-row single-log" v-for="(item, rowIndex) in logList" :key="`log-${item.operationTime || item.id || rowIndex}`">
            <div class="log-item">
              <div class="log-icon" :class="getStatusClass(item.result)"></div>
              <div class="log-content">
                <div class="log-time">{{
                  dayjs(item.operationTime).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
                <div class="log-action">完成一次操作</div>
                <div class="log-tag-box">
                  <div
                    :class="['log-tag', getStatusClass(it.color)]"
                    v-for="(it, index) in item.actionLabels"
                    :key="`tag-${it.desc || it.id || index}`"
                    >{{ it.desc }}</div
                  >
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 空数据 -->
        <GeegaEmpty v-else description="暂无数据" />
      </div>
    </ScreenBox>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import dayjs from 'dayjs';
import { V1LocationStudyOperationLogPagePost } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import { useErrorSound, type SoundType } from '/@/composables/useErrorSound';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { watchImmediate } from '@vueuse/core';
import { createLocalStorage } from '/@/utils/cache';

const props = defineProps<{
  detailId?: string;
  data?: any;
}>();

// 本地维护的日志列表
const localLogList = ref<any[]>([]);

// 初始化错误提示音
const { playErrorSound } = useErrorSound();

// 操作日志
const apiData = useAsyncData(
  async () => {
    if (Object.keys(props.data).length || !props.detailId) return { records: [] };
    const result = await V1LocationStudyOperationLogPagePost({
      pageSize: 10,
      currentPage: 1,
      data: {
        detailId: props.detailId,
      },
    });
    // 初始化本地日志列表
    localLogList.value = result.records || [];
    return result;
  },
  { records: [] }
);

watchImmediate(
  () => props.detailId,
  () => apiData.load()
);

// 清空本地日志
const clearLocalLog = () => {
  localLogList.value = [];
};

const logList = computed(() => {
  if (Object.keys(props.data).length && props.data.operationLog) {
    const newLog = props.data.operationLog;
    // 检查新日志是否已存在
    if (!localLogList.value.some((item) => item.id === newLog.id)) {
      localLogList.value = [newLog, ...localLogList.value];
      // 检查新日志的 result，如果是 0 或 2，播放对应的错误提示音
      if (newLog.result === 0 || newLog.result === 2) {
        // 从本地存储获取当前的声音类型设置
        const ls = createLocalStorage();
        const soundType = (ls.get('soundType') || 'rational') as SoundType;
        playErrorSound(newLog.actionLabels, soundType);
      }
    }
  }
  return localLogList.value;
});

const getStatusClass = (result: number) => {
  return {
    success: result === 1,
    error: result === 0,
    warning: result === 2,
  };
};

defineExpose({
  clearLocalLog,
});
</script>

<style lang="less" scoped>
.operation-log {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  height: 100%;
  flex: 3;
  min-width: 0;

  :deep(.screen-box) {
    height: 100%;
    padding: 0.74vw;
  }

  .log-list {
    display: flex;
    flex-direction: column;
    gap: 0.37vw;
    height: 100%;

    &.box-content {
      display: flex;
      flex-direction: column;
      gap: 0.37vw;
      position: relative;
      z-index: 1;
      overflow-x: hidden;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;

      &::-webkit-scrollbar {
        width: 0.28vw;
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 0.14vw;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    .log-row {
      display: flex;
      gap: 0.84vw;
      .log-item {
        display: flex;
        align-items: center;
        padding: 0.74vw;
        flex: 1;
        --ignore-dark-color: #ffffff0a;
        background: var(--ignore-dark-color);

        &:last-child {
          border-bottom: none;
        }

        .log-icon {
          width: 1.49vw;
          height: 1.39vw;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 0.74vw;
          font-size: max(12px, 0.74vw);

          &::after {
            content: '';
            display: block;
            width: 1.02vw;
            height: 1.25vw;
            background-size: cover;
          }

          &.success {
            &::after {
              --ignore-dark-image: url('@/assets/images/screen/log-success.png');
              background-image: var(--ignore-dark-image);
            }
          }

          &.error {
            &::after {
              width: 1.49vw;
              height: 1.37vw;
              --ignore-dark-image: url('@/assets/images/screen/log-error.png');
              background-image: var(--ignore-dark-image);
              background-size: 100% 100%;
            }
          }

          &.warning {
            &::after {
              --ignore-dark-image: url('@/assets/images/screen/log-warning.png');
              background-image: var(--ignore-dark-image);
            }
          }
        }

        .log-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 1.12vw;
          font-size: max(12px, 0.84vw);
          .log-action {
            color: rgba(255, 255, 255, 0.7);
          }
          .log-time {
            color: #ffffff;
          }
          .log-tag-box {
            display: flex;
            gap: 3px;
            flex: 1;
            .log-tag {
              margin-right: 0;
              padding: 0px 0.37vw;
              height: 1.12vw;
              line-height: 1.12vw;
              border-radius: 2px;
              border: 1px solid var(--Error-Error6-Normal, #29e8ab);
              color: var(--Error-Error6-Normal, #29e8ab);
              &.warning {
                border-color: #fc8800;
                color: #fc8800;
              }
              &.error {
                border-color: #f53f3f;
                color: #f53f3f;
              }
            }
          }
        }

        .log-status {
          border-radius: 0.09vw;
          font-size: max(12px, 0.65vw);
          --ignore-dark-success-color: #29e8ab;
          --ignore-dark-error-color: #f53f3f;
          --ignore-dark-warning-color: #e97a13;

          &::before {
            content: '';
            display: inline-block;
            width: 0.37vw;
            height: 0.37vw;
            border-radius: 50%;
            margin-right: 0.18vw;
            background-color: var(--ignore-dark-success-color);
          }

          &.success {
            color: var(--ignore-dark-success-color);

            &::before {
              background-color: var(--ignore-dark-success-color);
            }
          }

          &.error {
            color: var(--ignore-dark-error-color);

            &::before {
              background-color: var(--ignore-dark-error-color);
            }
          }

          &.warning {
            color: var(--ignore-dark-warning-color);

            &::before {
              background-color: var(--ignore-dark-warning-color);
            }
          }
        }
      }
      &.single-log {
        .log-item {
          width: 100%;
        }
      }
    }

    .cddc-web-empty {
      height: 100%;
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .operation-log {
    gap: 0.74vw;

    :deep(.screen-box) {
      padding: 0.74vw;
    }

    .log-list {
      gap: 0.37vw;

      .log-row {
        gap: 0.42vw;

        .log-item {
          padding: 0.74vw;
          font-size: max(12px, 0.84vw);

          .log-icon {
            width: 1.2vw;
            height: 1.1vw;
            margin-right: 0.6vw;

            &::after {
              width: 0.8vw;
              height: 1vw;
            }

            &.error::after {
              width: 1.2vw;
              height: 1.1vw;
            }
          }

          .log-content {
            gap: 0.8vw;
            font-size: max(12px, 0.74vw);
          }

          .log-status {
            font-size: max(12px, 0.65vw);
          }
        }
      }
    }
  }
}
</style>
