import type { FormSchema } from '@geega-ui-plus/geega-ui';
import type { AlgorithmItem } from '../types';
import { unitTree } from '/@/api/admin/project';
import { V1ManageCommonProjectTypeEnum } from '/@/api/cddc.req';
import { validateAlgorithmList } from '../../project/components/formData';

export const formSchemas: FormSchema[] = [
  {
    field: 'name',
    label: '名称',
    component: 'ApiSelect',
    required: true,
    slot: 'name',
  },
  {
    field: 'locationIds',
    label: '关联工位',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: async () => {
        const res = await unitTree();
        // 筛选只有type为STATION能选择
        const filterStations = (nodes) => {
          return nodes.filter((node) => {
            if (node.type !== 'STATION') {
              node.disabled = true;
            }
            if (node.childNodes && node.childNodes.length) {
              node.childNodes = filterStations(node.childNodes);
              return node.childNodes.length > 0;
            }
            return true;
          });
        };
        return filterStations(res);
      },
      fieldNames: {
        label: 'name',
        value: 'id',
        key: 'id',
        children: 'childNodes',
      },
      multiple: true,
      maxTagCount: 'responsive',
      treeNodeFilterProp: 'name',
      placeholder: '请选择',
    },
  },
  {
    field: 'requestDuration',
    label: '考核时长',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 9999,
      precision: 0,
      style: { width: '100%' },
      addonAfter: 'min',
    },
  },
  {
    field: 'requestFrequency',
    label: '要求有效次数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 9999,
      precision: 0,
      style: { width: '100%' },
    },
  },
  {
    field: 'requestQualificationRate',
    label: '合格率要求',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入',
      min: 0,
      max: 100,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '%',
    },
  },
  {
    field: 'requestActionRate',
    label: '动作达标率要求',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入',
      min: 0,
      max: 100,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '%',
    },
  },
  {
    field: 'countAlgorithm',
    label: '计次算法',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      placeholder: '请选择',
      api: V1ManageCommonProjectTypeEnum,
      disabled: true,
      fieldNames: {
        label: 'desc',
        value: 'code',
        key: 'code',
      },
    },
    rules: [
      {
        required: true,
        validator: async (_rule: any, value: string) => {
          if (!value) {
            return Promise.reject('请选择计次算法');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'requestEfficiency',
    label: '效率要求',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 9999,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '次/秒',
    },
  },
  {
    field: 'modelList',
    label: '算法关联',
    component: 'Select',
    slot: 'modelList',
    required: true,
    colProps: { span: 24 },
    rules: [
      {
        validator: async (_rule: any, value: AlgorithmItem[]) => {
          if (!value || value.length === 0) {
            return Promise.reject('请至少添加一个算法');
          }

          const validateResult = validateAlgorithmList(value);
          if (!validateResult.isValid) {
            // 只返回第一个错误信息
            return Promise.reject(validateResult.firstErrorMessage);
          }

          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
  },
];
