import type { FormSchema } from '@geega-ui-plus/geega-ui';
import type { AlgorithmItem, AlgorithmValidateError, AlgorithmValidateResult } from '../types';
import { unitTree } from '/@/api/admin/project';
import { V1ManageCommonProjectTypeEnum } from '/@/api/cddc.req';

// 算法列表校验函数
export const validateAlgorithmList = (algorithmList: AlgorithmItem[]): AlgorithmValidateResult => {
  const errors: AlgorithmValidateError[] = [];
  let hasError = false;

  // 检查 isIndicator 是否至少有一个为合格率
  const hasIndicatorOne = algorithmList.some((item) => item.modelJsonObj?.isIndicator === 1);
  if (algorithmList.length > 0 && !hasIndicatorOne) {
    hasError = true;
    errors[0] = { type: '至少需要一个算法的指标值为合格率，不能全部为达标率' };
    return {
      isValid: false,
      errors,
      firstErrorMessage: '至少需要一个算法的指标值为合格率，不能全部为达标率',
    };
  }

  // 找到第一个错误
  const firstError = algorithmList.reduce<{ index: number; message: string } | null>(
    (result, item, index) => {
      // 如果已经找到错误，直接返回
      if (result) return result;

      // 检查 type 是否为空
      if (!item.modelCode) {
        return {
          index,
          message: `第 ${index + 1} 项算法类型不能为空`,
        };
      }

      // 检查是否至少选择了一个复选框
      if (!item.modelJsonObj?.isQualified && !item.modelJsonObj?.isActionQualified) {
        return {
          index,
          message: `第 ${index + 1} 项至少需要选择一个指标`,
        };
      }

      return null;
    },
    null
  );

  // 如果找到错误，设置错误信息
  if (firstError) {
    hasError = true;
    errors[firstError.index] = { type: firstError.message };
  }

  return {
    isValid: !hasError,
    errors,
    firstErrorMessage: firstError?.message,
  };
};

export const formSchemas: FormSchema[] = [
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入项目名称',
      maxLength: 10,
    },
  },
  {
    field: 'locationIds',
    label: '关联工位',
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: async () => {
        const res = await unitTree();
        // 筛选只有type为STATION能选择
        const filterStations = (nodes) => {
          return nodes.filter((node) => {
            if (node.type !== 'STATION') {
              node.disabled = true;
            }
            if (node.childNodes && node.childNodes.length) {
              node.childNodes = filterStations(node.childNodes);
              return node.childNodes.length > 0;
            }
            return true;
          });
        };
        return filterStations(res);
      },
      fieldNames: {
        label: 'name',
        value: 'id',
        key: 'id',
        children: 'childNodes',
      },
      showArrow: true,
      multiple: true,
      maxTagCount: 'responsive',
      treeNodeFilterProp: 'name',
      placeholder: '请选择',
    },
  },
  // {
  //   field: 'requestDuration',
  //   label: '要求时长',
  //   component: 'InputNumber',
  //   required: true,
  //   componentProps: {
  //     placeholder: '请输入',
  //     min: 1,
  //     max: 9999,
  //     precision: 0,
  //     style: { width: '100%' },
  //     addonAfter: 'min',
  //   },
  // },
  {
    field: 'requestFrequency',
    label: '要求次数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 9999,
      precision: 0,
      style: { width: '100%' },
    },
  },
  {
    field: 'requestQualificationRate',
    label: '合格率要求',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入',
      min: 0,
      max: 100,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '%',
    },
  },
  {
    field: 'requestActionRate',
    label: '动作达标率要求',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入',
      min: 0,
      max: 100,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '%',
    },
  },
  {
    field: 'countAlgorithm',
    label: '计次算法',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      placeholder: '请选择',
      api: V1ManageCommonProjectTypeEnum,
      fieldNames: {
        label: 'desc',
        value: 'code',
        key: 'code',
      },
      // options: [
      //   { label: '拧紧类', value: AlgorithmTypeEnum.TIGHTENING },
      //   { label: '堵盖类', value: AlgorithmTypeEnum.PLUG },
      //   { label: '密封胶条类', value: AlgorithmTypeEnum.SEAL },
      //   { label: '管线安装类', value: AlgorithmTypeEnum.PIPELINE },
      //   { label: '线管插接类', value: AlgorithmTypeEnum.CONDUIT },
      // ],
    },
    rules: [
      {
        required: true,
        validator: async (_rule: any, value: string) => {
          if (!value) {
            return Promise.reject('请选择计次算法');
          }
          return Promise.resolve();
        },
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'requestEfficiency',
    label: '效率要求',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 9999,
      precision: 0,
      style: { width: '100%' },
      addonAfter: '次/秒',
    },
  },
  {
    field: 'modelList',
    label: '算法关联',
    component: 'Select',
    slot: 'modelList',
    required: true,
    colProps: { span: 24 },
    rules: [
      {
        validator: async (_rule: any, value: AlgorithmItem[]) => {
          if (!value || value.length === 0) {
            return Promise.reject('请至少添加一个算法');
          }

          const validateResult = validateAlgorithmList(value);
          if (!validateResult.isValid) {
            // 只返回第一个错误信息
            return Promise.reject(validateResult.firstErrorMessage);
          }

          return Promise.resolve();
        },
        trigger: 'change',
      },
    ],
  },
];
