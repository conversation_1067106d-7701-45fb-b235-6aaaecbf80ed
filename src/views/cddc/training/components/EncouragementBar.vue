<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import type { V1LocationHomeProgressStatisticsRecordIDGetResponseResult } from '/@/api/cddc.model';

const props = defineProps<{
  /**
   * 报表数据
   */
  data?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
}>();

// 计算鼓励信息
const encouragementData = computed(() => {
  const data = props.data;
  // 这里可以根据实际数据计算排名，暂时使用模拟数据
  const ranking = data?.ranking || Math.floor(Math.random() * 50) + 1;

  return {
    num: ranking,
    level: ranking <= 10 ? 'excellent' : ranking <= 30 ? 'good' : 'normal',
  };
});
</script>

<template>
  <div class="encouragement-bar">
    <ScreenBox :class="encouragementData.level">
      <div class="encouragement-content">
        <div class="encouragement-badge"></div>
        <div class="encouragement-text"
          >你的训练进度超过了<label class="num">{{ encouragementData.num }}</label
          >名工友</div
        >
      </div>
    </ScreenBox>
  </div>
</template>

<style lang="less" scoped>
.encouragement-bar {
  margin-bottom: 0.92vw;
}

.encouragement-content {
  display: flex;
  align-items: center;
  gap: 0.74vw;
  padding: 0.74vw;
  --ignore-bg-color: rgba(255,255,255, 0.04);
  background-color: var(--ignore-bg-color);
}

.encouragement-badge {
  width: 2.69vw;
  height: 1.38vw;
  background: url('/@/assets/images/screen/icon-cheer.png') no-repeat;
  background-size: 100% 100%;
}

.encouragement-text {
  font-size: 0.83vw;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
  margin-top: 1px;
  .num {
    --ignore-color: #29e8ab;
    color: var(--ignore-color);
    font-style: italic;
    font-weight: 700;
    font-size: 1.1vw;
    margin: 0 0.28vw;
  }
}

@media screen and (max-width: 1024px) {
  .encouragement-content {
    padding: 6px 12px;
    gap: 6px;
  }

  .encouragement-badge {
    font-size: 11px;
    padding: 3px 6px;
  }

  .encouragement-text {
    font-size: 13px;
  }
}
</style>
