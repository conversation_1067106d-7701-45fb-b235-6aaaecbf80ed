<template>
  <div class="exam-take-page">
    <a-page-header :title="pageTitle" class="page-header-fixed">
      <template #extra>
        <div class="time-info">
          <span class="time-label">考试时长：</span>
          <span class="time-value">{{ formatDuration(elapsedSeconds) }}</span>
        </div>
      </template>
    </a-page-header>

    <div class="content-container">
      <a-spin :spinning="loading" tip="加载中...">
        <template v-if="paperInfo && questions.length > 0">
          <div class="paper-info">
            <h1 class="paper-title">{{ paperInfo.title || '未命名试卷' }}</h1>
            <div class="paper-meta">
              <div class="meta-item">
                <span class="label">总分：</span>
                <span class="value">{{ totalScore }}分</span>
              </div>
              <div class="meta-item">
                <span class="label">合格分：</span>
                <span class="value">{{ paperInfo.standardScore || 0 }}分</span>
              </div>
              <div class="meta-item">
                <span class="label">题目数量：</span>
                <span class="value">{{ questions.length }}题</span>
              </div>
            </div>
          </div>

          <div class="questions-container">
            <AnswerQuestions
              :questions="questions"
              :questionTypes="paperInfo.questionTypeList"
              :userAnswers="userAnswers"
              @update:userAnswers="userAnswers = $event"
            />
          </div>

          <div class="actions-bar" v-if="examRecordId">
            <a-button @click="handleCancel">取消考试</a-button>
            <a-button type="primary" @click="handleSubmit" :loading="submitting">提交答案</a-button>
          </div>
        </template>

        <a-empty v-else-if="!loading" description="未找到试卷信息或试题" />
      </a-spin>
    </div>

    <!-- 确认提交弹窗 -->
    <a-modal
      v-model:visible="submitModalVisible"
      title="确认提交"
      @ok="confirmSubmit"
      @cancel="cancelSubmit"
      :okButtonProps="{ loading: submitting }"
    >
      <p>您确定要提交答案吗？提交后将无法修改。</p>
      <p>已答题数: {{ getAnsweredCount() }}/{{ questions.length }}</p>
    </a-modal>

    <!-- 取消考试弹窗 -->
    <a-modal
      v-model:visible="cancelModalVisible"
      title="确认取消"
      @ok="confirmCancel"
      @cancel="() => { cancelModalVisible = false }"
    >
      <p>您确定要取消考试吗？取消后将不保存已作答内容。</p>
    </a-modal>

    <!-- 考试结果弹窗 -->
    <a-modal
      v-model:visible="resultModalVisible"
      title="考试结果"
      :closable="false"
      :maskClosable="false"
      :footer="null"
    >
      <div class="result-container">
        <div class="result-icon">
          <check-circle-outlined v-if="examPassed" style="color: #52c41a; font-size: 48px;" />
          <close-circle-outlined v-else style="color: #f5222d; font-size: 48px;" />
        </div>
        <div class="result-title">{{ examPassed ? '恭喜您，考试通过！' : '很遗憾，考试未通过' }}</div>
        <div class="result-score">
          <span>您的得分：</span>
          <span class="score">{{ examResult.score || 0 }}</span>
          <span>分</span>
        </div>
        <div class="result-time">
          <span>考试用时：</span>
          <span>{{ formatDuration(examResult.duration || 0) }}</span>
        </div>
        <div class="result-actions">
          <a-button type="primary" @click="handleBackToLearning">返回学习</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  V1ManageExamPaperId,
  V1ManageExamPaperQuestionListPaperId,
  V1MobileStudyExamRecordStartPost,
  V1MobileStudyOverPost,
  V1MobileHomeLastedExamRecordPaperId
} from '/@/api/cddc.req';
import type {
  PaperElement,
  V1ManageExamPaperQuestionListPaperIDGetResponseResult,
  V1ManageExamPaperQuestionListPaperIDGetResponse,
  V1ManageExamPaperIDGetResponse
} from '/@/api/cddc.model';
import { message } from '@geega-ui-plus/ant-design-vue';
import { CheckCircleOutlined, CloseCircleOutlined } from '@geega-ui-plus/icons-vue';
import AnswerQuestions from '/@/components/Examination/AnswerQuestions.vue';
import { QUESTION_TYPE } from '/@/components/Examination/Questions/enum';

const route = useRoute();
const router = useRouter();

// 页面状态
const loading = ref(true);
const submitting = ref(false);
const pageTitle = ref('参加考试');
const submitModalVisible = ref(false);
const cancelModalVisible = ref(false);
const resultModalVisible = ref(false);

// 试卷信息
const paperId = computed(() => route.query.paperId as string);
const routeExamRecordId = computed(() => route.query.examRecordId as string);
const paperInfo = ref<PaperElement | null>(null);
const questions = ref<V1ManageExamPaperQuestionListPaperIDGetResponseResult[]>([]);
const totalScore = ref(0);

// 用户答案
const userAnswers = ref<Record<string, any>>({});
const examRecordId = ref(''); // 考试记录ID
const elapsedSeconds = ref(0); // 已用时间（秒）
let timer: number | null = null;

// 考试结果
const examResult = ref<{score?: number, duration?: number}>({});
const examPassed = computed(() => {
  if (!paperInfo.value || !examResult.value.score) return false;
  return examResult.value.score >= (paperInfo.value.standardScore || 0);
});

// 获取试卷信息
const fetchPaperInfo = async () => {
  if (!paperId.value) {
    message.error('未提供试卷ID');
    loading.value = false;
    return;
  }

  try {
    loading.value = true;

    // 获取试卷详情
    const paperResponse: V1ManageExamPaperIDGetResponse = await V1ManageExamPaperId({
      id: paperId.value
    });

    if (paperResponse) {
      paperInfo.value = paperResponse;
    } else {
      message.error('获取试卷信息失败');
      return;
    }

    // 获取试题列表
    const questionsResponse: V1ManageExamPaperQuestionListPaperIDGetResponse = await V1ManageExamPaperQuestionListPaperId({
      paperId: paperId.value
    });

    if (questionsResponse && Array.isArray(questionsResponse)) {
      questions.value = questionsResponse;

      // 计算总分
      totalScore.value = questions.value.reduce((total, question) => {
        return total + getQuestionScore(question);
      }, 0);

      // 初始化用户答案
      questions.value.forEach(q => {
        if (q.id) {
          if (q.questionType === QUESTION_TYPE.MULTIPLE_CHOICE) {
            userAnswers.value[q.id] = [];
          } else {
            userAnswers.value[q.id] = '';
          }
        }
      });

      // 开始考试
      await startExam();
    } else {
      message.error('获取试题列表失败');
    }
  } catch (error) {
    console.error('获取试卷信息失败:', error);
    message.error('获取试卷信息失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 开始考试
const startExam = async () => {
  try {
      examRecordId.value = routeExamRecordId.value;

      // 直接开始考试
      const examResponse = await V1MobileStudyExamRecordStartPost({
        id: examRecordId.value
      });

      if (examResponse) {
        startTimer();
      } else {
        message.error('开始考试失败');
      }
  } catch (error) {
    console.error('开始考试失败:', error);
    message.error('开始考试失败，请稍后再试');
  }
};

// 开始计时
const startTimer = () => {
  if (timer !== null) return;

  timer = window.setInterval(() => {
    elapsedSeconds.value += 1;
  }, 1000);
};

// 停止计时
const stopTimer = () => {
  if (timer !== null) {
    window.clearInterval(timer);
    timer = null;
  }
};

// 格式化时间
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${secs < 10 ? '0' + secs : secs}`;
};

// 获取题目分数
const getQuestionScore = (question: V1ManageExamPaperQuestionListPaperIDGetResponseResult) => {
  // 从试卷配置中查找对应题型和难度的分数设置
  if (paperInfo.value?.questionTypeList && question.questionType) {
    const typeConfig = paperInfo.value.questionTypeList.find(
      (n) => n.questionType === question.questionType &&
             n.difficultDegree?.toString() === question.difficultDegree?.toString()
    );

    if (typeConfig?.singleScore) {
      return typeConfig.singleScore;
    }
  }

  // 如果没有找到对应配置，根据题型设置默认分数
  const defaultScores: Record<string, number> = {
    [QUESTION_TYPE.SINGLE_CHOICE]: 3,
    [QUESTION_TYPE.MULTIPLE_CHOICE]: 4,
    [QUESTION_TYPE.JUDGMENT]: 2,
    [QUESTION_TYPE.SHORT_ANSWER]: 5,
    'ESSAY': 10,
    'CODING': 15
  };

  // 确保问题类型与QUESTION_TYPE枚举格式一致
  const questionType = question.questionType || '';
  return defaultScores[questionType] || 5;
};

// 获取已答题数量
const getAnsweredCount = () => {
  let count = 0;
  for (const id in userAnswers.value) {
    const answer = userAnswers.value[id];
    if (Array.isArray(answer)) {
      // 多选题：数组非空
      if (answer.length > 0) count++;
    } else if (typeof answer === 'string') {
      // 字符串类型：非空字符串
      if (answer.trim() !== '') count++;
    } else if (typeof answer === 'number') {
      // 数字类型：任何数字都算答题
      count++;
    } else if (answer) {
      // 其他非空值
      count++;
    }
  }
  return count;
};

// 提交按钮处理
const handleSubmit = () => {
  submitModalVisible.value = true;
};

// 确认提交
const confirmSubmit = async () => {
  try {
    submitting.value = true;

    // 准备提交数据
    const answerList = Object.keys(userAnswers.value).map(questionId => {
      let answer = userAnswers.value[questionId];

      // 处理答案格式
      if (Array.isArray(answer)) {
        // 多选题
        return {
          id: questionId,
          answer: answer
        };
      } else {
        // 单选题、判断题和简答题
        return {
          id: questionId,
          answer: [answer]
        };
      }
    });
    // 提交答案
    const response = await V1MobileStudyOverPost({
      id: examRecordId.value,
      answerList: answerList
    });

    if (response) {
      examResult.value = response;
      stopTimer();
      submitModalVisible.value = false;
      resultModalVisible.value = true;
    } else {
      message.error('提交答案失败');
    }
  } catch (error) {
    console.error('提交答案失败:', error);
    message.error('提交答案失败，请稍后再试');
  } finally {
    submitting.value = false;
  }
};

// 取消提交
const cancelSubmit = () => {
  submitModalVisible.value = false;
};

// 取消考试
const handleCancel = () => {
  cancelModalVisible.value = true;
};

// 确认取消考试
const confirmCancel = () => {
  stopTimer();
  cancelModalVisible.value = false;
  router.back();
};

// 返回学习页面
const handleBackToLearning = () => {
  router.back();
};

onMounted(() => {
  fetchPaperInfo();
});

onUnmounted(() => {
  stopTimer();
});
</script>

<style lang="less" scoped>
.exam-take-page {
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  height: 100%;
}

.page-header-fixed {
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 24px;

  .time-info {
    display: inline-flex;
    align-items: center;

    .time-label {
      color: rgba(0, 0, 0, 0.65);
    }

    .time-value {
      font-weight: 500;
      color: #00996b;
      margin-left: 4px;
    }
  }
  :deep(.cddc-ant-page-header-heading-title){
    line-height: inherit;
  }
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  overflow-y: auto;
}

.paper-info {
  background-color: #fff;
  padding: 16px 24px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .paper-title {
    font-size: 24px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 0;
  }

  .paper-meta {
    display: flex;
    flex-wrap: wrap;

    .meta-item {
      margin-right: 24px;
      color: rgba(0, 0, 0, 0.65);

      .label {
        font-size: 14px;
      }

      .value {
        font-weight: 500;
        color: #00996b;
      }
    }
  }
}

.questions-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.actions-bar {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 0;
  position: sticky;
  bottom: 0;
  background-color: #f0f2f5;
  margin-top: auto;
}

.result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;

  .result-icon {
    margin-bottom: 16px;
  }

  .result-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .result-score {
    margin-bottom: 8px;
    font-size: 16px;

    .score {
      font-size: 24px;
      color: #ff4d4f;
      font-weight: 700;
      margin: 0 4px;
    }
  }

  .result-time {
    margin-bottom: 24px;
    color: rgba(0, 0, 0, 0.65);
  }

  .result-actions {
    margin-top: 16px;
  }
}
</style>
