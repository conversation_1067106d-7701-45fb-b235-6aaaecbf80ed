<template>
  <div class="face-login-container">
    <div class="face-status">
      <CameraOutlined class="face-icon" />
      <p class="face-text">{{ isCameraActive ? '正在进行人脸识别...' : '请正对摄像头进行人脸识别...' }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { CameraOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useWebSocket } from '/@/utils/websocket';
import { useGlobSetting } from '/@/hooks/setting';
import md5 from 'crypto-js/md5';
interface FaceLoginEmits {
  (e: 'loginSuccess', loginData: any): void;
}

interface Props {
  deviceIp?: string;
}

const props = withDefaults(defineProps<Props>(), {
  deviceIp: '',
});

const emit = defineEmits<FaceLoginEmits>();

const { createMessage } = useMessage();
const { socketUrl } = useGlobSetting();

// 人脸识别登录相关状态
const isCameraActive = ref(false);
const faceWsInstance = ref<ReturnType<typeof useWebSocket> | null>(null);

// 初始化人脸识别WebSocket连接
const initFaceWebSocket = async () => {
  cleanupFaceWebSocket();

  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    headers: {
      deviceIp: props.deviceIp,
    },
    onMessage: handleFaceWebSocketMessage,
    onError: (error) => console.error('人脸识别WebSocket错误:', error),
    onClose: (event) => {
      console.log('人脸识别WebSocket连接已关闭:', event);
      isCameraActive.value = false;
    },
    onOpen: () => {
      console.log('人脸识别WebSocket连接已建立');
      isCameraActive.value = true;
    },
    createMessage: {
      action: 'swipeCardLogin',
      content: {
        deviceIp: props.deviceIp,
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {},
    },
  });

  faceWsInstance.value = ws;
  ws.connect();
};

// 清理人脸识别WebSocket连接
const cleanupFaceWebSocket = () => {
  if (faceWsInstance.value) {
    faceWsInstance.value.disconnect();
    faceWsInstance.value = null;
  }
  isCameraActive.value = false;
};

// 处理人脸识别WebSocket消息
const handleFaceWebSocketMessage = async (data: any) => {
  if (data.status == 'success') return;
  console.log('人脸识别消息:', data);

  // 人脸识别成功，触发登录事件
  if (data.employeeId || data.userId) {
    emit('loginSuccess', {
      username: md5(data.employeeId).toString(),
      password: data.employeeId || data.userId,
      loginType: 4, // 人脸识别登录类型
      employeeId: data.employeeId,
      userId: data.userId,
    });
  }
};

// 暴露方法给父组件
defineExpose({
  initFaceWebSocket,
  cleanupFaceWebSocket,
});

// 组件挂载时不自动初始化，等待父组件调用
onMounted(() => {
  // 不自动初始化WebSocket，等待父组件调用
});

// 组件卸载时清理
onUnmounted(() => {
  cleanupFaceWebSocket();
});
</script>

<style lang="less" scoped>
.face-login-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;

  .face-status {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: rgba(0, 153, 107, 0.1);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;

    .face-icon {
      font-size: 48px;
      color: #00996b;
      margin-bottom: 16px;
      animation: pulse 1.5s infinite;
      filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
    }

    .face-text {
      margin: 0;
      color: rgba(255, 255, 255, 0.85);
      font-size: 16px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
