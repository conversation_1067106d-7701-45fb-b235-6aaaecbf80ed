<template>
  <div class="qrcode-login-container">
    <div class="qrcode-status" v-if="!qrcodeExpired">
      <div class="qrcode-wrapper">
        <img v-if="qrcodeUrl" :src="qrcodeUrl" alt="登录二维码" class="qrcode-image" />
        <div v-else class="qrcode-loading">
          <QrcodeOutlined class="qrcode-icon" />
          <p class="qrcode-text">正在生成二维码...</p>
        </div>
      </div>
      <p class="qrcode-tip">请使用小程序扫码登录</p>
    </div>
    <div class="qrcode-expired" v-else>
      <QrcodeOutlined class="qrcode-expired-icon" />
      <p class="qrcode-expired-text">二维码已过期</p>
      <a-button type="primary" @click="refreshQrcode" :loading="qrcodeLoading">
        <ReloadOutlined />
        刷新二维码
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { QrcodeOutlined, ReloadOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  V1OpenApiUserLoginScanQrcode,
  V1OpenApiUserLoginScanLoginStatus
} from '/@/api/cddc.req';

interface QrcodeLoginEmits {
  (e: 'loginSuccess', userInfo: any): void;
}

interface Props {
  deviceIp?: string;
}

const props = withDefaults(defineProps<Props>(), {
  deviceIp: '',
});

const emit = defineEmits<QrcodeLoginEmits>();

const { createMessage } = useMessage();

// 扫码登录相关状态
const qrcodeUrl = ref<string>('');
const qrcodeToken = ref<string>('');
const qrcodeExpired = ref(false);
const qrcodeLoading = ref(false);
const pollingTimer = ref<NodeJS.Timeout | null>(null);
const qrcodeTimer = ref<NodeJS.Timeout | null>(null);

// 生成二维码
const generateQrcode = async () => {
  try {
    // 调用后端接口生成二维码
    const response = await V1OpenApiUserLoginScanQrcode({
      width: 200,
      height: 200
    });

    // 检查响应格式
    if (typeof response === 'string') {
      // 如果返回的是字符串，可能是base64图片或者包含token信息的JSON字符串
      try {
        // 尝试解析为JSON，看是否包含token信息
        const parsedData = JSON.parse(response);
        if (parsedData.token) {
          qrcodeToken.value = parsedData.token;
          // 如果有二维码图片数据
          if (parsedData.qrcodeImage) {
            qrcodeUrl.value = parsedData.qrcodeImage;
          } else {
            // 如果没有图片数据，使用原始响应作为图片
            qrcodeUrl.value = response;
          }
        } else {
          // 如果解析失败或没有token，使用响应作为图片，生成临时token
          qrcodeUrl.value = response;
          qrcodeToken.value = `scan_${Date.now()}_${props.deviceIp}`;
        }
      } catch (parseError) {
        // 如果不是JSON格式，直接作为图片使用
        qrcodeUrl.value = response;
        qrcodeToken.value = `scan_${Date.now()}_${props.deviceIp}`;
      }
    } else {
      // 如果返回的是对象格式（实际上API定义为string，这个分支可能不会执行）
      // 但为了健壮性，仍然保留这个处理逻辑
      const responseObj = response as any;
      qrcodeToken.value = responseObj.token || `scan_${Date.now()}_${props.deviceIp}`;
      qrcodeUrl.value = responseObj.qrcodeImage || responseObj.qrcodeData || response;
    }

    qrcodeExpired.value = false;

    // 设置二维码过期时间（5分钟）
    qrcodeTimer.value = setTimeout(() => {
      qrcodeExpired.value = true;
      stopPolling();
    }, 5 * 60 * 1000);

  } catch (error) {
    console.error('生成二维码失败:', error);
    createMessage.error('生成二维码失败');
    throw error;
  }
};

// 开始轮询登录状态
const startPolling = () => {
  stopPolling(); // 先停止之前的轮询

  const poll = async () => {
    try {
      if (qrcodeExpired.value) {
        return;
      }

      const response = await V1OpenApiUserLoginScanLoginStatus({
        token: qrcodeToken.value
      });

      // 检查登录状态
      if (response && response.result) {
        const loginData = response.result;

        // 根据实际API返回结构处理数据
        // 如果登录成功，触发登录事件
        if (loginData.status === 'success' || loginData.loginStatus === 'success') {
          const userInfo = loginData.userInfo || loginData.user;
          if (userInfo) {
            emit('loginSuccess', {
              username: userInfo.username || userInfo.employeeId || userInfo.account,
              password: userInfo.token || userInfo.password || userInfo.employeeId,
              loginType: 5, // 扫码登录类型
              userInfo: userInfo,
            });
            stopPolling();
            return;
          }
        }

        // 如果二维码过期
        if (loginData.status === 'expired' || loginData.loginStatus === 'expired') {
          qrcodeExpired.value = true;
          stopPolling();
          return;
        }

        // 如果是等待扫码状态，继续轮询
        if (loginData.status === 'waiting' || loginData.loginStatus === 'waiting') {
          // 继续轮询
        }
      }

      // 继续轮询
      if (!qrcodeExpired.value) {
        pollingTimer.value = setTimeout(poll, 3000); // 3秒轮询一次
      }
    } catch (error) {
      console.error('轮询登录状态失败:', error);
      // 出错时继续轮询，但增加间隔时间
      if (!qrcodeExpired.value) {
        pollingTimer.value = setTimeout(poll, 5000); // 出错时5秒后重试
      }
    }
  };

  // 立即开始第一次轮询
  poll();
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 刷新二维码
const refreshQrcode = async () => {
  try {
    qrcodeLoading.value = true;
    stopPolling();
    await generateQrcode();
    startPolling();
  } catch (error) {
    console.error('刷新二维码失败:', error);
    createMessage.error('刷新二维码失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 清理扫码登录相关资源
const cleanupQrcodeLogin = () => {
  stopPolling();
  if (qrcodeTimer.value) {
    clearTimeout(qrcodeTimer.value);
    qrcodeTimer.value = null;
  }
  qrcodeExpired.value = false;
};

// 初始化扫码登录
const initQrcodeLogin = async () => {
  try {
    qrcodeLoading.value = true;
    await generateQrcode();
    startPolling();
  } catch (error) {
    console.error('初始化扫码登录失败:', error);
    createMessage.error('初始化扫码登录失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  initQrcodeLogin,
  cleanupQrcodeWebSocket: cleanupQrcodeLogin,
});

// 组件挂载时不自动初始化，等待父组件调用
onMounted(() => {
  // 不自动初始化，等待父组件调用
});

// 组件卸载时清理
onUnmounted(() => {
  cleanupQrcodeLogin();
});
</script>

<style lang="less" scoped>
.qrcode-login-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;

  .qrcode-status {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background: rgba(0, 153, 107, 0.1);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;

    .qrcode-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 10px;

      .qrcode-image {
        width: 160px;
        height: 160px;
        border-radius: 8px;
        background: #ffffff;
        padding: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .qrcode-loading {
        width: 160px;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 8px;

        .qrcode-icon {
          font-size: 48px;
          color: #00996b;
          margin-bottom: 16px;
          animation: pulse 1.5s infinite;
          filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
        }

        .qrcode-text {
          margin: 0;
          color: rgba(255, 255, 255, 0.85);
          font-size: 16px;
        }
      }
    }

    .qrcode-tip {
      margin: 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 14px;
      text-align: center;
    }
  }

  .qrcode-expired {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    .qrcode-expired-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.45);
      margin-bottom: 16px;
    }

    .qrcode-expired-text {
      margin: 0 0 24px 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 16px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
