<template>
  <div class="qrcode-login-container">
    <div class="qrcode-status" v-if="!qrcodeExpired">
      <div class="qrcode-wrapper">
        <img v-if="qrcodeUrl" :src="qrcodeUrl" alt="登录二维码" class="qrcode-image" />
        <div v-else class="qrcode-loading">
          <QrcodeOutlined class="qrcode-icon" />
          <p class="qrcode-text">正在生成二维码...</p>
        </div>
      </div>
      <p class="qrcode-tip">请使用小程序扫码登录</p>
    </div>
    <div class="qrcode-expired" v-else>
      <QrcodeOutlined class="qrcode-expired-icon" />
      <p class="qrcode-expired-text">二维码已过期</p>
      <a-button type="primary" @click="refreshQrcode" :loading="qrcodeLoading">
        <ReloadOutlined />
        刷新二维码
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { QrcodeOutlined, ReloadOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useWebSocket } from '/@/utils/websocket';
import { useGlobSetting } from '/@/hooks/setting';
import { toDataURL } from '/@/utils/qrcode';
// import { generateQrcodeLogin, getQrcodeStatus } from '/@/api/qrcode-login';

interface QrcodeLoginEmits {
  (e: 'loginSuccess', userInfo: any): void;
}

interface Props {
  deviceIp?: string;
}

const props = withDefaults(defineProps<Props>(), {
  deviceIp: '',
});

const emit = defineEmits<QrcodeLoginEmits>();

const { createMessage } = useMessage();
const { socketUrl } = useGlobSetting();

// 扫码登录相关状态
const qrcodeUrl = ref<string>('');
const qrcodeToken = ref<string>('');
const qrcodeExpired = ref(false);
const qrcodeLoading = ref(false);
const qrcodeWsInstance = ref<ReturnType<typeof useWebSocket> | null>(null);
const qrcodeTimer = ref<NodeJS.Timeout | null>(null);

// 生成二维码
const generateQrcode = async () => {
  try {
    // TODO: 等接口完成后启用以下代码
    // const response = await generateQrcodeLogin();
    // qrcodeToken.value = response.token;
    // const qrcodeData = response.qrcodeData;

    // 临时模拟数据，等接口完成后替换
    const mockToken = `qrcode_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    qrcodeToken.value = mockToken;
    const qrcodeData = `{"action":"login","token":"${mockToken}","timestamp":${Date.now()}}`;

    // 生成二维码图片
    qrcodeUrl.value = await toDataURL(qrcodeData, {
      width: 200,
      margin: 2,
      color: {
        dark: '#00996b',
        light: '#ffffff'
      }
    });

    qrcodeExpired.value = false;

    // 设置二维码过期时间（5分钟）
    qrcodeTimer.value = setTimeout(() => {
      qrcodeExpired.value = true;
      cleanupQrcodeWebSocket();
    }, 5 * 60 * 1000);

  } catch (error) {
    console.error('生成二维码失败:', error);
    throw error;
  }
};

// 刷新二维码
const refreshQrcode = async () => {
  try {
    qrcodeLoading.value = true;
    cleanupQrcodeWebSocket();
    await generateQrcode();
    initQrcodeWebSocket();
  } catch (error) {
    console.error('刷新二维码失败:', error);
    createMessage.error('刷新二维码失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 初始化扫码登录WebSocket连接
const initQrcodeWebSocket = async () => {
  cleanupQrcodeWebSocket();

  const ws = useWebSocket({
    url: `ws://${socketUrl}/ws`,
    headers: {
      deviceIp: props.deviceIp,
    },
    onMessage: handleQrcodeWebSocketMessage,
    onError: (error) => console.error('扫码登录WebSocket错误:', error),
    onClose: (event) => console.log('扫码登录WebSocket连接已关闭:', event),
    onOpen: () => console.log('扫码登录WebSocket连接已建立'),
    createMessage: {
      action: 'qrcodeLogin',
      content: {
        deviceIp: props.deviceIp,
        token: qrcodeToken.value,
      },
    },
    heartbeatMessage: {
      action: 'heartbeat',
      content: {},
    },
  });

  qrcodeWsInstance.value = ws;
  ws.connect();
};

// 处理扫码登录WebSocket消息
const handleQrcodeWebSocketMessage = async (data: any) => {
  if (data.status === 'success') return;

  console.log('扫码登录消息:', data);

  // 扫码成功，获取到用户信息，触发登录事件
  if (data.action === 'qrcodeScanned' && data.userInfo) {
    emit('loginSuccess', {
      username: data.userInfo.username || data.userInfo.employeeId,
      password: data.userInfo.token || data.userInfo.employeeId,
      loginType: 5, // 扫码登录类型
      userInfo: data.userInfo,
    });
  }

  // 二维码过期
  if (data.action === 'qrcodeExpired') {
    qrcodeExpired.value = true;
    cleanupQrcodeWebSocket();
  }
};

// 清理扫码登录WebSocket连接
const cleanupQrcodeWebSocket = () => {
  if (qrcodeWsInstance.value) {
    qrcodeWsInstance.value.disconnect();
    qrcodeWsInstance.value = null;
  }
  if (qrcodeTimer.value) {
    clearTimeout(qrcodeTimer.value);
    qrcodeTimer.value = null;
  }
  qrcodeExpired.value = false;
};

// 初始化扫码登录
const initQrcodeLogin = async () => {
  try {
    qrcodeLoading.value = true;
    await generateQrcode();
    initQrcodeWebSocket();
  } catch (error) {
    console.error('初始化扫码登录失败:', error);
    createMessage.error('初始化扫码登录失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  initQrcodeLogin,
  cleanupQrcodeWebSocket,
});

// 组件挂载时不自动初始化，等待父组件调用
onMounted(() => {
  // 不自动初始化，等待父组件调用
});

// 组件卸载时清理
onUnmounted(() => {
  cleanupQrcodeWebSocket();
});
</script>

<style lang="less" scoped>
.qrcode-login-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;

  .qrcode-status {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background: rgba(0, 153, 107, 0.1);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;

    .qrcode-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 10px;

      .qrcode-image {
        width: 160px;
        height: 160px;
        border-radius: 8px;
        background: #ffffff;
        padding: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .qrcode-loading {
        width: 160px;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 8px;

        .qrcode-icon {
          font-size: 48px;
          color: #00996b;
          margin-bottom: 16px;
          animation: pulse 1.5s infinite;
          filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
        }

        .qrcode-text {
          margin: 0;
          color: rgba(255, 255, 255, 0.85);
          font-size: 16px;
        }
      }
    }

    .qrcode-tip {
      margin: 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 14px;
      text-align: center;
    }
  }

  .qrcode-expired {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    .qrcode-expired-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.45);
      margin-bottom: 16px;
    }

    .qrcode-expired-text {
      margin: 0 0 24px 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 16px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
