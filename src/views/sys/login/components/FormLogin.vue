<template>
  <div class="form-login-container">
    <a-form :model="formData" @finish="handleSubmit">
      <a-form-item name="username" :rules="usernameRules">
        <a-input
          v-model:value="formData.username"
          size="large"
          :placeholder="usernamePlaceholder"
        >
          <template #prefix>
            <UserOutlined />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item name="password" :rules="passwordRules">
        <a-input-password
          v-model:value="formData.password"
          size="large"
          placeholder="请输入密码"
        >
          <template #prefix>
            <LockOutlined />
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" size="large" block :loading="loading">
          登录
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { UserOutlined, LockOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { userStore } from '/@/store/modules/user';
import { useGo } from '/@/hooks/web/usePage';
import {
  V1OpenApiUserLoginGucPost,
  V1OpenApiUserLoginPublicKey,
} from '/@/api/cddc.req';
// @ts-ignore
import JSEncrypt from 'jsencrypt';

interface FormLoginEmits {
  (e: 'loginSuccess'): void;
}

interface Props {
  loginType: 'password' | 'domain';
  loading?: boolean;
}

interface LoginParams {
  username: string;
  password: string;
  loginType?: number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<FormLoginEmits>();

const { createMessage } = useMessage();
const go = useGo();

// 表单数据
const formData = ref<Pick<LoginParams, 'username' | 'password'>>({
  username: '',
  password: '',
});

// 根据登录类型计算占位符文本
const usernamePlaceholder = computed(() => {
  return props.loginType === 'domain' ? '请输入域账号' : '请输入账号';
});

// 根据登录类型计算验证规则
const usernameRules = computed(() => {
  const message = props.loginType === 'domain' ? '请输入域账号' : '请输入账号';
  return [{ required: true, message }];
});

const passwordRules = [{ required: true, message: '请输入密码' }];

// 获取公钥
const getPublicKey = async (username: string) => {
  try {
    // 设置登录超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 5000);
    });

    const loginPublicKey = await Promise.race([
      V1OpenApiUserLoginPublicKey({ username }),
      timeoutPromise,
    ]);

    if (typeof loginPublicKey !== 'string') {
      throw new Error('获取公钥失败');
    }

    return loginPublicKey;
  } catch (error) {
    console.error('获取公钥失败:', error);
    throw error;
  }
};

// 密码加密
const encryptPassword = (password: string, publicKey: string) => {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(publicKey);
  return encrypt.encrypt(password);
};

// 处理表单提交
const handleSubmit = async (values: LoginParams) => {
  try {
    // 设置登录超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 5000);
    });

    // 获取公钥并加密密码
    const publicKey = await getPublicKey(values.username);
    const encryptedPassword = encryptPassword(values.password, publicKey);

    if (!encryptedPassword) {
      throw new Error('密码加密失败');
    }

    // 执行登录
    const loginInfo = await Promise.race([
      V1OpenApiUserLoginGucPost({
        ...values,
        password: encryptedPassword,
        loginType: props.loginType === 'domain' ? 3 : 1, // 域账号登录类型为3，密码登录类型为1
      }),
      timeoutPromise,
    ]);

    await userStore.setLoginToken(loginInfo);
    await afterLogin();
    emit('loginSuccess');

  } catch (error: any) {
    console.error(`${props.loginType === 'domain' ? '域账号' : '密码'}登录失败:`, error);

    if (error instanceof Error && error.message === '登录超时') {
      createMessage.error('登录超时，页面将自动刷新');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      createMessage.error('登录失败，请检查账号密码');
    }
  }
};

// 登录成功后的处理
const afterLogin = async () => {
  await userStore.recordLoginTime();
  go('/home');
};

// 重置表单
const resetForm = () => {
  formData.value = {
    username: '',
    password: '',
  };
};

// 监听登录类型变化，重置表单
watch(() => props.loginType, () => {
  resetForm();
});

// 暴露方法给父组件
defineExpose({
  resetForm,
});
</script>

<style lang="less" scoped>
.form-login-container {
  padding: 0;
}
</style>
