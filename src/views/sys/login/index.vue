<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/resource/img/logo.png" alt="logo" class="logo" />
        <h1 class="title">道场智能培训系统</h1>
      </div>
      <div class="login-content">
        <a-tabs v-model:activeKey="activeKey" @change="handleTabChange">
          <a-tab-pane key="card" tab="刷卡登录">
            <CardLogin
              ref="cardLoginRef"
              :device-ip="ipAddress"
              @login-success="handleCardLogin"
            />
          </a-tab-pane>
          <a-tab-pane key="face" tab="刷脸登录">
            <FaceLogin
              ref="faceLoginRef"
              :device-ip="ipAddress"
              @login-success="handleFaceLogin"
            />
          </a-tab-pane>
          <a-tab-pane key="qrcode" tab="扫码登录">
            <QrcodeLogin
              ref="qrcodeLoginRef"
              :device-ip="ipAddress"
              @login-success="handleQrcodeLogin"
            />
          </a-tab-pane>
          <a-tab-pane key="password" tab="密码登录">
            <FormLogin
              ref="passwordLoginRef"
              login-type="password"
              :loading="loading"
              @login-success="handleFormLoginSuccess"
            />
          </a-tab-pane>
          <a-tab-pane key="domain" tab="域账号登录">
            <FormLogin
              ref="domainLoginRef"
              login-type="domain"
              :loading="loading"
              @login-success="handleFormLoginSuccess"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

import { useMessage } from '/@/hooks/web/useMessage';
import { userStore } from '/@/store/modules/user';
import { useGo } from '/@/hooks/web/usePage';
import type { LoginParams } from '/@/types/auth';
import {
  V1LocationHomeCurrentIp,
  V1OpenApiUserLoginGucPost,
  V1OpenApiUserLoginPublicKey,
} from '/@/api/cddc.req';

// @ts-ignore
import JSEncrypt from 'jsencrypt';

import QrcodeLogin from './components/QrcodeLogin.vue';
import CardLogin from './components/CardLogin.vue';
import FaceLogin from './components/FaceLogin.vue';
import FormLogin from './components/FormLogin.vue';

const { createMessage } = useMessage();
const go = useGo();

const activeKey = ref('card');
const loading = ref(false);
const ipAddress = ref<string>('');
const qrcodeLoginRef = ref();
const cardLoginRef = ref();
const faceLoginRef = ref();
const passwordLoginRef = ref();
const domainLoginRef = ref();

// 处理标签页切换
const handleTabChange = (key: string) => {
  // 清理所有WebSocket连接
  if (qrcodeLoginRef.value) {
    qrcodeLoginRef.value.cleanupQrcodeWebSocket();
  }
  if (cardLoginRef.value) {
    cardLoginRef.value.cleanupWebSocket();
  }
  if (faceLoginRef.value) {
    faceLoginRef.value.cleanupFaceWebSocket();
  }

  // 等待下一个tick后重新初始化对应的组件
  nextTick(() => {
    switch (key) {
      case 'face':
        if (faceLoginRef.value) {
          faceLoginRef.value.initFaceWebSocket();
        }
        break;
      case 'card':
        if (cardLoginRef.value) {
          cardLoginRef.value.initWebSocket();
        }
        break;
      case 'qrcode':
        if (qrcodeLoginRef.value) {
          qrcodeLoginRef.value.initQrcodeLogin();
        }
        break;
      // 密码登录和域账号登录不需要建立WebSocket连接
      case 'password':
      case 'domain':
      default:
        break;
    }
  });
};

// 获取本机IP地址
const fetchIpAddress = async () => {
  try {
    const response = await V1LocationHomeCurrentIp();
    if (response) {
      ipAddress.value = response;
      return ipAddress.value;
    } else {
      console.warn('获取IP地址返回了意外的格式:', response);
      return '';
    }
  } catch (error) {
    console.error('获取IP地址失败:', error);
    return '';
  }
};

// 处理刷卡登录成功
const handleCardLogin = async (loginData: any) => {
  try {
    loading.value = true;
    await handlePasswordLogin(loginData);
  } catch (error) {
    console.error('刷卡登录失败:', error);
    createMessage.error('登录失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 处理人脸识别登录成功
const handleFaceLogin = async (loginData: any) => {
  try {
    loading.value = true;
    await handlePasswordLogin(loginData);
  } catch (error) {
    console.error('人脸识别登录失败:', error);
    createMessage.error('人脸识别登录失败，请重试');
  } finally {
    loading.value = false;
  }
};



// 页面加载时获取IP地址并初始化默认标签页
onMounted(async () => {
  // 获取IP地址
  await fetchIpAddress();

  // 初始化默认标签页（刷卡登录）
  nextTick(() => {
    if (activeKey.value === 'card' && cardLoginRef.value) {
      cardLoginRef.value.initWebSocket();
    }
  });
});

// 组件卸载时清理WebSocket连接
onUnmounted(() => {
  if (faceLoginRef.value) {
    faceLoginRef.value.cleanupFaceWebSocket();
  }

  if (cardLoginRef.value) {
    cardLoginRef.value.cleanupWebSocket();
  }

  if (qrcodeLoginRef.value) {
    qrcodeLoginRef.value.cleanupQrcodeWebSocket();
  }
});

// 密码登录（保留给其他组件调用）
const handlePasswordLogin = async (values: LoginParams) => {
  try {
    loading.value = true;

    // 设置登录超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 5000);
    });

    const loginPublicKey = await Promise.race([
      V1OpenApiUserLoginPublicKey({ username: values.username }),
      timeoutPromise,
    ]);

    if (typeof loginPublicKey !== 'string') {
      throw new Error('获取公钥失败');
    }

    // 使用 JSEncrypt 加密密码
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(loginPublicKey);
    const encryptedPassword = encrypt.encrypt(values.password);

    if (!encryptedPassword) {
      throw new Error('密码加密失败');
    }

    const loginInfo = await Promise.race([
      V1OpenApiUserLoginGucPost({
        ...values,
        password: encryptedPassword,
        loginType: values.loginType || 1,
      }),
      timeoutPromise,
    ]);

    await userStore.setLoginToken(loginInfo);
    await afterLogin();
  } catch (error) {
    if (error instanceof Error && error.message === '登录超时') {
      createMessage.error('登录超时，页面将自动刷新');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      createMessage.error('登录失败，请检查账号密码');
    }
  } finally {
    loading.value = false;
  }
};

// 登录成功后的处理
const afterLogin = async () => {
  // createMessage.success('登录成功');
  await userStore.recordLoginTime();
  // 获取重定向地址
  go('/home');
};

// 处理扫码登录成功
const handleQrcodeLogin = async (loginData: any) => {
  try {
    loading.value = true;
    await handlePasswordLogin(loginData);
  } catch (error) {
    console.error('扫码登录失败:', error);
    createMessage.error('扫码登录失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 处理表单登录成功
const handleFormLoginSuccess = () => {
  // 表单组件内部已经处理了登录逻辑，这里只需要处理成功后的状态
  console.log('表单登录成功');
};
</script>

<style lang="less" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #1a1a1a;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 153, 107, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 75% 75%, rgba(0, 153, 107, 0.1) 0%, transparent 40%);

  .login-box {
    // width: 440px;
    padding: 40px;
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 12px 48px rgba(0, 153, 107, 0.2);
      border-color: rgba(0, 153, 107, 0.3);
    }

    .login-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 40px;

      .logo {
        width: 48px;
        height: 48px;
        margin-right: 16px;
        transition: transform 0.3s ease;
        filter: brightness(0.9);
      }

      .title {
        margin: 0;
        font-size: 30px;
        font-weight: 600;
        color: #00996b;
        text-shadow: 0 0 20px rgba(0, 153, 107, 0.3);
      }
    }

    .login-content {
      :deep(.cddc-ant-tabs-nav) {
        margin-bottom: 32px;
      }

      :deep(.cddc-ant-tabs-content) {
        min-height: 220px;
      }

      :deep(.cddc-ant-tabs-tabpane) {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      :deep(.cddc-ant-tabs-tab) {
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.65);
        padding: 12px 16px; // 增加标签页内边距
        min-width: 80px; // 设置最小宽度
        text-align: center;

        &:hover {
          color: #00996b;
        }

        &.cddc-ant-tabs-tab-active .cddc-ant-tabs-tab-btn {
          color: #00996b;
        }
      }

      :deep(.cddc-ant-tabs-ink-bar) {
        background: #00996b;
      }

      :deep(.cddc-ant-input-affix-wrapper) {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 8px;
        transition: all 0.3s ease;

        input {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.85);

          &::placeholder {
            color: rgba(255, 255, 255, 0.25);
          }
        }

        .anticon {
          color: rgba(255, 255, 255, 0.45);
        }

        &:hover,
        &:focus {
          border-color: #00996b;
          box-shadow: 0 0 0 2px rgba(0, 153, 107, 0.1);
          background-color: rgba(255, 255, 255, 0.08);
        }
      }

      :deep(.cddc-ant-btn-primary) {
        height: 44px;
        font-size: 16px;
        border-radius: 8px;
        background: #00996b;
        border: none;
        transition: all 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        box-shadow: 0 4px 12px rgba(0, 153, 107, 0.3);

        &:hover {
          opacity: 0.9;
          box-shadow: 0 6px 16px rgba(0, 153, 107, 0.4);
        }
      }
    }



    .card-login-container {
      .card-input {
        position: absolute;
        opacity: 0;
        pointer-events: none;
      }
    }
  }
}


</style>
