<template>
  <MenuItem :key="menuKey">
    <span class="flex items-center">
      <Icon :icon="icon" class="mr-1" />
      <span>{{ text }}</span>
      <!-- suffix -->
      <slot name="suffix" />
    </span>
  </MenuItem>
</template>
<script lang="ts">
import { Menu } from '@geega-ui-plus/ant-design-vue';

import { defineComponent } from 'vue';

import { Icon } from '@geega-ui-plus/geega-ui';
import { propTypes } from '/@/utils/propTypes';

export default defineComponent({
  name: 'DropdownMenuItem',
  components: { MenuItem: Menu.Item, Icon },
  props: {
    menuKey: propTypes.string.def(''),
    text: propTypes.string.def(''),
    icon: propTypes.string.def(''),
  },
});
</script>
