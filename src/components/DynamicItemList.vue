<script lang="ts" generic="T extends {} = any" setup>
import { useInjectFormItemContext } from '@geega-ui-plus/ant-design-vue/es/form';
import { useVModel } from '@vueuse/core';
import { computed, watch } from 'vue';

export interface DynamicItemListProps<T> {
  value?: T[];
  maxCount?: number;
  addBtnText?: string;
}

const props = defineProps<DynamicItemListProps<T>>();

const emit = defineEmits(['update:value', 'add-item', 'remove-item']);

const vValue = useVModel(props, 'value', emit);

const formItemCtx = useInjectFormItemContext();

const disabledAddBtn = computed(() => {
  if (!props.maxCount) return false;

  return (vValue.value?.length || 0) >= props.maxCount;
});

watch(
  vValue,
  () => {
    formItemCtx?.onFieldChange();
  },
  {
    deep: true,
  }
);

function handleAdd() {
  const evt = new Event('add-item');
  emit('add-item', evt);

  if (evt.defaultPrevented) {
    return;
  }

  const newValue = vValue.value || [];

  newValue.push({} as T);

  vValue.value = newValue;
}

function handleRemove(index: number) {
  const evt = new Event('remove-item');

  emit('remove-item', evt, { index });

  if (evt.defaultPrevented) {
    return;
  }

  vValue.value?.splice(index, 1);
}
</script>

<template>
  <div>
    <a-form-item-rest>
      <div class="flex flex-col gap-1">
        <template v-for="(item, idx) in vValue || []" :key="`item-${idx}-${JSON.stringify(item)}`">
          <slot :item="item" :index="idx" :removeItem="() => handleRemove(idx)"></slot>
        </template>
      </div>
    </a-form-item-rest>
    <a-button class="mt-2" type="primary" ghost @click="handleAdd" :disabled="disabledAddBtn">
      <slot name="add-btn"> {{ addBtnText || '新增' }} </slot>
    </a-button>
  </div>
</template>

<style lang="less" scoped></style>
