import { groupBy } from 'lodash-es';
import type { QuestionBankDetailListElement } from '/@/api/cddc.model';
import { QUESTION_TYPE } from './Questions';
import { QUESTION_DIFFICULT_DEGREE } from '/@/enums/questionEnum';

/**
 * Examples:
 *
 * 0 => A
 * 1 => B
 *
 * @param num
 * @returns
 */
export function number2alphaChar(num: number) {
  const alpha = String.fromCharCode(num + 65);
  return alpha;
}

/**
 *
 * Examples:
 *
 * 0 => 一
 * 1 => 二
 * 2 => 三
 *
 * @param num 仅支持十万以内的数字，不包括十万
 * @returns
 */
export function number2chineseChar(num: number): string {
  if (num < 0 || num >= 100000) {
    throw new Error('数字超出范围: ' + num);
  }

  if (num < 10) {
    return singleNumberToChineseChar(num);
  }

  const units = ['', '十', '百', '千', '万'];
  const digits = num.toString().split('').map(Number).reverse();
  let result = '';

  let hasZero = false;
  for (let i = 0; i < digits.length; i++) {
    if (digits[i] === 0) {
      if (!hasZero && result.length) {
        hasZero = true;
        result = '零' + result;
      }
      continue;
    }

    result = singleNumberToChineseChar(digits[i]) + units[i] + result;
  }

  // 一十几 => 十几
  if (result.startsWith('一十')) {
    result = result.substring(1);
  }

  return result;
}

/**
 *
 * @param num 0 - 9
 * @returns
 */
function singleNumberToChineseChar(num: number): string {
  const chineseChars = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  return chineseChars[num];
}

interface IGroupedQuestions<T extends QuestionBankDetailListElement> {
  orderType: string;
  questions: T[];
}

export function groupQuestions<T extends QuestionBankDetailListElement>(
  questions: T[] = []
): IGroupedQuestions<T>[] {
  const _questions = groupBy(
    questions || [],
    (item) => `${item.questionType}-${item.difficultDegree}`
  );

  const result: IGroupedQuestions<T>[] = [];

  const difficultyOrder = [
    QUESTION_DIFFICULT_DEGREE.EASY,
    QUESTION_DIFFICULT_DEGREE.NORMAL,
    QUESTION_DIFFICULT_DEGREE.HARD,
  ];

  const groupOrder = [
    QUESTION_TYPE.FILL_IN_THE_BLANK,
    QUESTION_TYPE.SINGLE_CHOICE,
    QUESTION_TYPE.JUDGMENT,
    QUESTION_TYPE.MULTIPLE_CHOICE,
    QUESTION_TYPE.SHORT_ANSWER,
  ].flatMap((t) => difficultyOrder.map((d) => `${t}-${d}`));

  for (const orderType of groupOrder) {
    const groupedQuestions = _questions[orderType];
    if (!groupedQuestions?.length) {
      continue;
    }

    result.push({
      orderType,
      questions: groupedQuestions,
    });
  }

  return result;
}
