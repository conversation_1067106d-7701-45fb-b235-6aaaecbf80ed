export enum QUESTION_TYPE {
  SINGLE_CHOICE = 'SINGLE_CHOICE',
  JUDGMENT = 'JUDGMENT',
  FILL_IN_THE_BLANK = 'FILL_IN_THE_BLANK',
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  SHORT_ANSWER = 'SHORT_ANSWER',
}

export const QuestionTypeOptions = [
  {
    label: '单选题',
    value: QUESTION_TYPE.SINGLE_CHOICE,
  },
  {
    label: '判断题',
    value: QUESTION_TYPE.JUDGMENT,
  },
  {
    label: '填空题',
    value: QUESTION_TYPE.FILL_IN_THE_BLANK,
  },
  {
    label: '多选题',
    value: QUESTION_TYPE.MULTIPLE_CHOICE,
  },
  // {
  //   label: '简答题',
  //   value: QUESTION_TYPE.SHORT_ANSWER,
  // },
];
