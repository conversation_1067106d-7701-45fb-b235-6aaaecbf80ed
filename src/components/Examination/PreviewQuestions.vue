<script lang="ts" setup>
import { computed } from 'vue';
import { QuestionComponentsMap, type IQuestion } from './Questions';
import type {
  ExamRecordElement,
  ResultAnswerList,
  ResultQuestionTypeList,
} from '/@/api/cddc.model';
import { getDictLabel } from '/@/dict';
import { QUESTION_TYPE, QuestionTypeOptions } from './Questions/enum';
import { groupQuestions, number2chineseChar } from './utils';
import { QuestionDifficultOptions } from '/@/enums/questionEnum';

export interface UserAnswerInfo {
  info: ExamRecordElement;
  answers: ResultAnswerList[];
}

export interface ExamPreviewProps {
  questionTypes?: ResultQuestionTypeList[];
  questions: IQuestion[];
  userAnswer?: UserAnswerInfo;
}

const props = defineProps<ExamPreviewProps>();

const groupedQuestions = computed(() => {
  const grouped = groupQuestions(props.questions);

  return grouped.map((item) => {
    const [questionType, difficulty] = item.orderType.split('-');

    const typeConfig = props.questionTypes?.find(
      (n) => n.questionType === questionType && n.difficultDegree?.toString() === difficulty
    );
    const singleScore = typeConfig?.singleScore || 0;

    const unit = questionType === QUESTION_TYPE.FILL_IN_THE_BLANK ? '空' : '题';

    const difficultyName = getDictLabel(QuestionDifficultOptions, difficulty).toString();
    const typeName = getDictLabel(QuestionTypeOptions, questionType).toString();
    const name = `${typeName}-${difficultyName}`;

    const totalDescription = `共 ${item.questions.length} ${unit}`;
    const scoreDescription = typeConfig ? `每题 ${singleScore} 分，` : ``;

    const title = `${name}（${scoreDescription}${totalDescription}）`;

    return {
      name,
      typeConfig,
      title: title,
      score: singleScore,
      orderType: item.orderType,
      questions: item.questions,
    };
  });
});

function getUserQuestionAnswer(question: IQuestion) {
  const answers = props.userAnswer?.answers;

  if (!answers) {
    return;
  }

  const answer = answers.find((n) => n.questionId === question.id);

  return answer?.answerList;
}
</script>

<template>
  <div class="questions">
    <div class="grouped-questions" v-for="(gQuestions, idx) in groupedQuestions">
      <div class="type-title">
        {{ number2chineseChar(idx + 1) }} 、
        <slot name="group-title" :group="gQuestions">
          {{ gQuestions.title }}
        </slot>
      </div>

      <div class="question" v-for="(question, idx) in gQuestions.questions">
        <component
          :is="QuestionComponentsMap[question.questionType!]"
          :question="question"
          :index="idx + 1"
          :key="question.id"
          show-answer
          :user-answer="getUserQuestionAnswer(question)"
        />
        <slot name="question-item-suffix" :question="question"> </slot>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.questions {
  display: flex;
  flex-direction: column;
}

.type-title {
  font-weight: bold;
  margin-bottom: 24px;
}

.question {
  :deep(p) {
    margin: 0;
  }
}
</style>
