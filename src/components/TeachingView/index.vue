<template>
  <div class="teaching-view">
    <component
      :is="currentView.component"
      v-bind="currentView.props"
      v-if="currentView"
      class="view-content"
    />
    <div class="view-controls">
      <a-button type="default" shape="round" @click="toggleVideoMode">
        <template #icon>
          <SwapOutlined />
        </template>
        {{ isVideoMode ? '返回图示' : '视频教程' }}
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import VideoPlayer from './components/VideoPlayer.vue';
import ScreenPointMap from './components/ScreenPointMap.vue';
import CappingPointMap from './components/CappingPointMap.vue';
import SealingRubberMap from './components/SealingRubberMap.vue';
import PipelineInstallationMap from './components/PipelineInstallationMap.vue';
import HarnessConnectorMap from './components/HarnessConnectorMap.vue';
import { SwapOutlined } from '@geega-ui-plus/icons-vue';
import { V1ManageProcessAlgorithmWithCode } from '/@/api/cddc.req';
import { useGlobSetting } from '/@/hooks/setting';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';

export type DiagramType =
  | 'tighten'
  | 'capping'
  | 'sealingRubber'
  | 'pipelineInstallation'
  | 'harnessConnector';

interface View {
  component: any;
  props?: Record<string, any>;
}

const props = defineProps<{
  pointData?: any[];
  diagramType: DiagramType | undefined;
}>();

const emit = defineEmits<{
  (e: 'viewChange', isVideo: boolean): void;
}>();

const { trainingUrl } = useGlobSetting();

// 是否显示视频模式
const isVideoMode = ref(false);
const currentVideoUrl = ref('');

// 获取视频URL
const fetchVideoUrl = async () => {
  try {
    const videoInfo = await V1ManageProcessAlgorithmWithCode({
      code: props.diagramType,
    });
    currentVideoUrl.value = videoInfo?.videoUrl || trainingUrl || '/example.mp4';
  } catch (error) {
    console.error('获取视频信息失败:', error);
    currentVideoUrl.value = trainingUrl || '/example.mp4';
  }
};

// 切换视频/图示模式
const toggleVideoMode = async () => {
  const newMode = !isVideoMode.value;
  if (newMode) {
    await fetchVideoUrl();
  }
  isVideoMode.value = newMode;
  emit('viewChange', newMode);
};

// 获取当前视图配置
const currentView = computed<View>(() => {
  if (isVideoMode.value) {
    return {
      component: VideoPlayer,
      props: {
        src: currentVideoUrl.value,
        title: '视频教程',
      },
    };
  }
  if (props.diagramType) {
    let DiagramComponent;
    switch (props.diagramType) {
      case 'capping':
        DiagramComponent = CappingPointMap;
        break;
      case 'sealingRubber':
        DiagramComponent = SealingRubberMap;
        break;
      case 'pipelineInstallation':
        DiagramComponent = PipelineInstallationMap;
        break;
      case 'harnessConnector':
        DiagramComponent = HarnessConnectorMap;
        break;
      default:
        DiagramComponent = ScreenPointMap;
    }
    return {
      component: DiagramComponent,
      props: {
        data: props.pointData,
        type: props.diagramType,
      },
    };
  } else {
    return {
      component: GeegaEmpty,
      props: {
        description: '暂无数据',
      },
    };
  }
});
</script>

<style lang="less" scoped>
.teaching-view {
  flex: 1;
  position: relative;
  display: flex;
  min-width: 0;
  padding-top: 4.5vh;

  .view-content {
    flex: 1;
    min-width: 0;
  }

  .view-controls {
    position: absolute;
    right: 1vw;
    top: 1.5vh;
    display: flex;
    gap: 8px;
    z-index: 10;

    .cddc-ant-btn {
      font-size: 14px;
      padding: 4px 12px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      gap: 4px;

      &.active {
        background-color: var(--ant-primary-color);
        color: #fff;
      }

      :deep(.anticon) {
        font-size: 16px;
      }
    }
  }
}

@media screen and (max-width: 1024px) {
  .teaching-view {
    .view-controls {
      right: 0.8vw;
      top: 0.8vh;
      gap: 6px;

      .cddc-ant-btn {
        font-size: 12px;
        height: 28px;
        padding: 3px 10px;
      }
    }
  }
}
</style>
