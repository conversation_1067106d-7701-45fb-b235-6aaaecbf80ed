import { BufferGeometry, Group, LineSegments, Mesh, MeshBasicMaterial, Object3D } from 'three';
import { modelLoader } from './modelLoader';
import { defHttp } from '/@/utils/http/axios';
import { getObjectCenterPoint, getObjectSize, type IDisposable } from '../ThreeJs';
import { downloadCache } from './downloadCache';

export class ModelFile extends Object3D implements IDisposable {
  model?: Object3D;

  cancelHandler = new AbortController();

  url?: string;

  async loadModel(url: string, dataBuffer?: ArrayBuffer) {
    const ext = url.split('.').pop() || '';

    const loader = modelLoader.getLoader(ext);
    if (!loader) {
      throw new Error(`${ext} file not support!`);
    }

    this.url = url;

    const data = dataBuffer ?? (await this._fetchModel(url));
    // const data = dataBuffer ?? (await this._fetchModelByPart(url));

    this.model = await loader.parse(data, url);

    covertModel(this.model);

    const centerNegate = getObjectCenterPoint(this.model).negate();
    this.model.position.copy(centerNegate);

    this.add(this.model);
  }

  async _fetchModel(url: string) {
    if (this.cancelHandler.signal.aborted) {
      throw new Error('canceled!');
    }

    this.cancelHandler.abort();
    this.cancelHandler = new AbortController();

    if (downloadCache.has(url)) {
      return downloadCache.get(url)!;
    }

    const resp = await defHttp.request(
      {
        url,
        signal: this.cancelHandler.signal,
        timeout: 120 * 1000,
        responseType: 'arraybuffer',
      },
      {
        isTransformRequestResult: false,
        joinTime: false,
      }
    );

    downloadCache.set(url, resp);
    return resp as ArrayBuffer;
  }

  async _fetchModelByPart(url: string) {
    if (this.cancelHandler.signal.aborted) {
      throw new Error('canceled!');
    }

    this.cancelHandler.abort();
    this.cancelHandler = new AbortController();

    if (downloadCache.has(url)) {
      return downloadCache.get(url)!;
    }

    const partSize = 1000 * 1000 * 20;

    const downloaded = {
      currentRangeStart: 0,
      data: [] as Blob[],
    };

    while (true) {
      const resp = await defHttp.getAxios().request({
        url,
        signal: this.cancelHandler.signal,
        timeout: 120 * 1000,
        responseType: 'blob',
        headers: {
          range: `bytes=${downloaded.currentRangeStart}-${downloaded.currentRangeStart + partSize}`,
        },
      });

      const totalSize = +resp.headers['content-range']?.split('/').pop()!;

      downloaded.data.push(resp.data);

      downloaded.currentRangeStart += partSize + 1;

      if (downloaded.currentRangeStart > totalSize) {
        break;
      }
      // throw new Error('test');
    }

    const data = await new Blob(downloaded.data).arrayBuffer();

    downloadCache.set(url, data);
    return data as ArrayBuffer;
  }

  dispose(): void {
    this.cancelHandler.abort();

    this.removeFromParent();
  }
}

function covertModel(scene: Object3D) {
  const needRemoveItems: Object3D[] = [];

  scene.traverse((item) => {
    if (item instanceof Mesh) {
      if (item.material instanceof MeshBasicMaterial) {
        needRemoveItems.push(item);
      } else {
        item.castShadow = true;
      }
    }

    if (item instanceof LineSegments) {
      needRemoveItems.push(item);
    }
  });

  if (needRemoveItems.length) {
    const _remove = new Group();
    _remove.add(...needRemoveItems);
  }

  scene.rotation.set(-Math.PI / 2, 0, Math.PI / 2);

  const size = getObjectSize(scene);
  const scale = 5 / size.length();
  scene.scale.setScalar(scale);

  return scene;
}
