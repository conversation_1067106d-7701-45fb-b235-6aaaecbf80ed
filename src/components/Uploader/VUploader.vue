<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import {
  convertFileSize,
  getResourceType,
  uploadApi,
  VideoIcon,
  type UploadApi,
  type UploadedResponse,
} from './utils';
import { message } from '@geega-ui-plus/ant-design-vue';
import { Upload, type UploadProps } from '@geega-ui-plus/ant-design-vue/es';
import { Icon } from '@geega-ui-plus/geega-ui';
import type { VUploadFile } from './types';
import { onUnmounted, reactive } from 'vue';
import { isObjectLike } from 'lodash-es';

export interface VUploaderProps {
  /**
   * 上传的 API
   */
  api?: UploadApi;
  /**
   * 支持的文件类型，默认 `.jpg,.jpeg,.png,.gif,.bmp`
   */
  accept?: string;
  /**
   * 支持上传的文件大小，单位 KB，默认不限制
   */
  maxSize?: number;
  /**
   * 最多支持上传的文件个数，默认不限制
   */
  maxCount?: number;
  /**
   * 是否多选，默认 true
   */
  multiple?: boolean;
  /**
   * 文件列表
   */
  modelValue?: VUploadFile[];
}

const props = withDefaults(defineProps<VUploaderProps>(), {
  accept: '.jpg,.jpeg,.png,.gif,.bmp',
  multiple: true,
});

const emit = defineEmits(['update:modelValue']);

const files = useVModel(props, 'modelValue');

const preview = reactive({
  visible: false,
  type: 'image' as 'image' | 'video',
  url: '',
});

const previewUrls: string[] = [];

onUnmounted(() => {
  previewUrls.forEach((item) => {
    URL.revokeObjectURL(item);
  });
});

const handlePreview: UploadProps['onPreview'] = (file) => {
  const resp = file.response as UploadedResponse | null;

  const url = resp?.url || file.url || file.thumbUrl;

  if (!url) {
    return;
  }

  const name = file.name || url;

  const type = /\.mp4$/.test(name) ? 'video' : 'image';

  preview.type = type;
  preview.visible = true;
  preview.url = url;
};

const customRequest: UploadProps['customRequest'] = async (opt) => {
  const req = props.api || uploadApi;

  try {
    const file = opt.file as File;
    // 确保后缀为小写
    const fixedName = file.name.replace(/\.\w+$/, (n) => n.toLowerCase());
    const fixedFile = new File([file], fixedName, { type: file.type });

    const info = await req(fixedFile, opt);

    opt.onSuccess?.(info);
  } catch (error: any) {
    const err =
      error instanceof Error
        ? error
        : new Error((isObjectLike(error) ? error.msg : '') || '上传失败');

    opt.onError?.(err);
  }
};

const checkBeforeUpload: UploadProps['beforeUpload'] = (file, fileList) => {
  const valid = isValid();

  if (!valid) {
    return Upload.LIST_IGNORE;
  }

  return true;

  function isValid() {
    let valid = false;

    const currentFileCount = files.value?.length || 0;
    const currentFileIdx = fileList.indexOf(file);
    const restCount = (props.maxCount || Infinity) - (currentFileCount + currentFileIdx + 1);

    if (restCount < 0) {
      message.warn(`文件上传个数最多支持 ${props.maxCount} 个`);
      return valid;
    }

    const ext = file.name.split(/\./g).pop();

    if (!ext || !props.accept.includes('.' + ext.toLowerCase())) {
      message.warn(`请上传 ${props.accept} 格式的文件`);
      return valid;
    }

    if (props.maxSize && file.size / 1024 > props.maxSize) {
      message.warn(`请上传小于 ${convertFileSize(props.maxSize)} 的文件`);

      return valid;
    }

    return true;
  }
};

const generatePreviewFileURL: UploadProps['previewFile'] = async (file) => {
  const isVideo = 'name' in file && getResourceType(file.name) === 'video';

  if (isVideo) {
    return VideoIcon;
  } else {
    const url = URL.createObjectURL(file);
    previewUrls.push(url);

    return url;
  }
};

function onLoadVideoError(e: Event) {
  message.error('视频加载失败，请联系管理员查看错误详情');
  console.error('视频加载失败，请联系管理员查看错误详情', e);
}
</script>

<template>
  <div class="upload-image">
    <a-upload
      v-model:fileList="files"
      :customRequest="customRequest"
      :multiple="multiple"
      :accept="accept"
      :before-upload="checkBeforeUpload"
      :previewFile="generatePreviewFileURL"
      @preview="handlePreview"
      list-type="picture-card"
      v-bind="$attrs"
    >
      <slot>
        <div
          class="color-#666"
          v-if="props.maxCount == null || (files?.length || 0) < props.maxCount"
        >
          <Icon icon="ant-design:plus-outlined" />
          <div class="ant-upload-text">点击上传</div>
        </div>
      </slot>
    </a-upload>

    <a-modal
      v-model:visible="preview.visible"
      title="预览"
      :footer="null"
      width="80vw"
      destroyOnClose
    >
      <div class="pt-10px flex items-center justify-center h-70vh">
        <img
          v-if="preview.type === 'image'"
          alt="preview"
          class="object-scale-down m-auto max-h-full max-w-full"
          :src="preview.url"
        />
        <video
          v-if="preview.type === 'video'"
          controls
          autoplay
          alt="preview"
          class="object-scale-down m-auto max-h-full max-w-full"
          :src="preview.url"
          @error="onLoadVideoError"
        />
      </div>
    </a-modal>
  </div>
</template>

<style lang="less" scoped></style>
