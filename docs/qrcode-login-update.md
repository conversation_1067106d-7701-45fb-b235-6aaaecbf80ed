# 扫码登录功能更新说明

## 更新内容

已完善扫码登录逻辑，使用新的后端API接口并实现轮询机制。修复了图片流获取的问题。

## 主要变更

### 1. API接口更新
- 使用 `defHttp.get` 直接调用 `/v1/open-api/user-login/scan/qrcode` 生成二维码
- 使用 `V1OpenApiUserLoginScanLoginStatus` 查询登录状态
- 移除了WebSocket依赖，改用轮询机制
- 修复了图片流获取的类型错误问题

### 2. 轮询机制
- 每3秒轮询一次登录状态
- 出错时5秒后重试
- 二维码过期时自动停止轮询
- 登录成功时立即停止轮询

### 3. 二维码生成逻辑
- 支持多种响应格式：URL地址、base64图片、blob数据
- 自动处理不同类型的图片数据
- 使用时间戳+设备IP生成轮询token
- 设置5分钟过期时间，自动清理blob URL

### 4. 状态处理
- 支持 `waiting`、`success`、`expired` 等状态
- 兼容不同的API响应格式
- 自动处理用户信息映射

## 使用流程

1. 用户选择"扫码登录"标签页
2. 系统调用 `V1OpenApiUserLoginScanQrcode` 生成二维码
3. 开始轮询 `V1OpenApiUserLoginScanLoginStatus` 查询状态
4. 用户扫码后，移动端确认登录
5. 轮询检测到登录成功，触发登录事件
6. 自动跳转到首页

## 技术细节

### 轮询间隔
- 正常轮询：3秒
- 错误重试：5秒
- 过期时间：5分钟

### 数据格式兼容
- 支持图片流地址格式的二维码
- 自动从URL参数中提取token
- 自动生成临时token作为备用方案

### 错误处理
- 网络错误时继续轮询
- 二维码生成失败时显示错误提示
- 过期时提供刷新功能

## 注意事项

1. **Token获取**：优先从图片URL参数中提取token，如果无法提取则使用时间戳+设备IP作为临时token

2. **图片地址**：后端返回的图片流地址会直接用作二维码图片的src属性

3. **状态映射**：登录状态检查支持多种字段名（status、loginStatus），用户信息支持多种字段名（userInfo、user）

4. **性能优化**：轮询机制在组件卸载时会自动清理，避免内存泄漏

## 问题修复

### 图片流获取错误
**问题**：使用 `V1OpenApiUserLoginScanQrcode` 时出现 `TypeError: Reflect.has called on non-object` 错误

**原因**：API适配器期望返回包装的响应对象，但图片流可能直接返回二进制数据

**解决方案**：
- 改用 `defHttp.get` 直接调用API端点
- 支持多种响应格式的自动处理
- 添加blob URL的内存管理

## 后续优化建议

1. 根据实际API响应格式调整数据解析逻辑
2. 可考虑添加扫码进度提示
3. 可添加扫码失败的重试机制
4. 可优化轮询间隔的动态调整
