# 扫码登录功能实现说明

## 功能概述

扫码登录功能允许用户通过小程序或APP扫描网页端生成的二维码来完成登录授权，提供更便捷的登录体验。

## 前端实现

### 1. 页面结构

在登录页面新增了"扫码登录"标签页，包含以下元素：
- 二维码显示区域
- 加载状态提示
- 过期状态处理
- 刷新二维码按钮

### 2. 核心功能

#### 2.1 二维码生成
- 调用后端接口生成唯一的登录token
- 将token等信息编码为二维码
- 设置二维码过期时间（5分钟）

#### 2.2 WebSocket连接
- 建立WebSocket连接监听扫码状态
- 处理扫码成功、过期等状态变化
- 自动清理连接和定时器

#### 2.3 登录流程
- 用户扫码后，移动端确认登录
- WebSocket接收到登录确认消息
- 自动完成网页端登录跳转

### 3. 文件修改

#### 3.1 主要文件
- `src/views/sys/login/index.vue` - 登录页面主文件
- `src/api/qrcode-login.ts` - 扫码登录API接口定义

#### 3.2 新增功能
- 扫码登录标签页UI
- 二维码生成和显示逻辑
- WebSocket消息处理
- 过期和刷新机制

## 后端接口对接

### 需要实现的接口

#### 1. 生成二维码接口
```
POST /v1/open-api/user-login/qrcode/generate
Response: {
  token: string,      // 唯一登录token
  qrcodeData: string, // 二维码数据
  expiresIn: number   // 过期时间（秒）
}
```

#### 2. 查询扫码状态接口
```
GET /v1/open-api/user-login/qrcode/status?token={token}
Response: {
  status: 'waiting' | 'scanned' | 'confirmed' | 'expired' | 'cancelled',
  userInfo?: {
    username: string,
    employeeId: string,
    token: string
  }
}
```

#### 3. 确认登录接口
```
POST /v1/open-api/user-login/qrcode/confirm
Request: {
  token: string,
  userInfo: {
    username: string,
    employeeId: string
  }
}
```

#### 4. 取消登录接口
```
POST /v1/open-api/user-login/qrcode/cancel
Request: {
  token: string
}
```

### WebSocket消息格式

#### 1. 建立连接消息
```json
{
  "action": "qrcodeLogin",
  "content": {
    "deviceIp": "*************",
    "token": "qrcode_token_123"
  }
}
```

#### 2. 扫码成功消息
```json
{
  "action": "qrcodeScanned",
  "userInfo": {
    "username": "user123",
    "employeeId": "emp001",
    "token": "login_token_456"
  }
}
```

#### 3. 二维码过期消息
```json
{
  "action": "qrcodeExpired"
}
```

## 移动端集成

### 小程序/APP需要实现的功能

1. **扫码功能**
   - 调用相机扫描二维码
   - 解析二维码中的登录token

2. **用户确认**
   - 显示登录确认界面
   - 展示要登录的设备信息
   - 用户确认或取消登录

3. **登录授权**
   - 调用确认登录接口
   - 传递用户身份信息

## 使用流程

1. 用户在网页端选择"扫码登录"
2. 系统生成二维码并建立WebSocket连接
3. 用户使用小程序/APP扫描二维码
4. 移动端显示登录确认界面
5. 用户确认登录后，移动端调用确认接口
6. 网页端通过WebSocket接收到登录成功消息
7. 自动完成登录并跳转到首页

## 注意事项

1. **安全性**
   - 二维码token应具有唯一性和时效性
   - WebSocket连接需要验证设备IP
   - 登录确认需要用户主动操作

2. **用户体验**
   - 二维码过期后提供刷新功能
   - 显示清晰的状态提示信息
   - 支持取消登录操作

3. **错误处理**
   - 网络异常时的重试机制
   - WebSocket连接断开的处理
   - 接口调用失败的提示

## 待完成工作

1. 后端接口开发和联调
2. 移动端扫码功能开发
3. WebSocket消息格式确认
4. 安全性测试和优化
5. 用户体验优化
